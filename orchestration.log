cument, visual content)?\n\nIf no further context is provided, I will proceed by initiating a generic sample action workflow (for example, creating a general informational document or slide deck on a random topic) and delegate to the DiscoveryMasterEmployee for creative task execution. Please confirm if this approach is suitable or provide any additional preferences.', 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 3187, 'completion_tokens': 229}, 'message_type': 'structured_message', 'metadata': {'content': 'To proceed with the request to "take anything" in the current workflow context, I need to ensure that the intended action and expected output are properly defined for task execution. Based on your recent queries (such as "generate a PPT about ruh.ai" and requests to "continue"), it appears you may want a creative or generic sample deliverable, but the goal and use case are ambiguous.\n\nTo enable effective task execution, could you clarify any of the following (optional):\n- Are you seeking a sample task, creative example, or a demonstration of a workflow?\n- Should the topic relate to ruh.ai, or is any topic acceptable?\n- Is there a preferred format or style for the output (e.g., text summary, document, visual content)?\n\nIf no further context is provided, I will proceed by initiating a generic sample action workflow (for example, creating a general informational document or slide deck on a random topic) and delegate to the DiscoveryMasterEmployee for creative task execution. Please confirm if this approach is suitable or provide any additional preferences.', 'content_type': 'response_to_user'}}
2025-07-16 15:33:36 - AgentExecutor - DEBUG - Content extracted: To proceed with the request to "take anything" in the current workflow context, I need to ensure tha...
2025-07-16 15:33:36 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': '3214420a-7214-486c-ab9f-dca590d87ccd', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'message_streaming', 'agent_response': {'content': 'To proceed with the request to "take anything" in the current workflow context, I need to ensure that the intended action and expected output are properly defined for task execution. Based on your recent queries (such as "generate a PPT about ruh.ai" and requests to "continue"), it appears you may want a creative or generic sample deliverable, but the goal and use case are ambiguous.\n\nTo enable effective task execution, could you clarify any of the following (optional):\n- Are you seeking a sample task, creative example, or a demonstration of a workflow?\n- Should the topic relate to ruh.ai, or is any topic acceptable?\n- Is there a preferred format or style for the output (e.g., text summary, document, visual content)?\n\nIf no further context is provided, I will proceed by initiating a generic sample action workflow (for example, creating a general informational document or slide deck on a random topic) and delegate to the DiscoveryMasterEmployee for creative task execution. Please confirm if this approach is suitable or provide any additional preferences.', 'source': 'OrchestratorEmployee', 'models_usage': {'prompt_tokens': 3187, 'completion_tokens': 229}, 'message_type': 'structured_message', 'metadata': {'content': 'To proceed with the request to "take anything" in the current workflow context, I need to ensure that the intended action and expected output are properly defined for task execution. Based on your recent queries (such as "generate a PPT about ruh.ai" and requests to "continue"), it appears you may want a creative or generic sample deliverable, but the goal and use case are ambiguous.\n\nTo enable effective task execution, could you clarify any of the following (optional):\n- Are you seeking a sample task, creative example, or a demonstration of a workflow?\n- Should the topic relate to ruh.ai, or is any topic acceptable?\n- Is there a preferred format or style for the output (e.g., text summary, document, visual content)?\n\nIf no further context is provided, I will proceed by initiating a generic sample action workflow (for example, creating a general informational document or slide deck on a random topic) and delegate to the DiscoveryMasterEmployee for creative task execution. Please confirm if this approach is suitable or provide any additional preferences.', 'content_type': 'response_to_user'}}, 'success': True, 'message': 'Orchestration team response', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-07-16 15:33:42 - AgentExecutor - DEBUG - Result consumer received message: Offset=26862
2025-07-16 15:33:42 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '3214420a-7214-486c-ab9f-dca590d87ccd', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'message_streaming', 'agent_response': {'content': '', 'source': 'UserProxyAgent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'user_input_request', 'metadata': {'user_input_requested': True, 'event_type': 'user_input_request', 'prompt': '', 'team_conversation_id': 'c349277e-a6f3-44f2-8437-e0bd86b56ab7'}}, 'success': True, 'message': 'Human input requested by the team', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-07-16 15:33:42 - AgentExecutor - DEBUG - Agent response extracted: {'content': '', 'source': 'UserProxyAgent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'user_input_request', 'metadata': {'user_input_requested': True, 'event_type': 'user_input_request', 'prompt': '', 'team_conversation_id': 'c349277e-a6f3-44f2-8437-e0bd86b56ab7'}}
2025-07-16 15:33:42 - AgentExecutor - DEBUG - Content extracted: None...
2025-07-16 15:33:42 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': '3214420a-7214-486c-ab9f-dca590d87ccd', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'message_streaming', 'agent_response': {'content': '', 'source': 'UserProxyAgent', 'models_usage': {'prompt_tokens': 0, 'completion_tokens': 0}, 'message_type': 'user_input_request', 'metadata': {'user_input_requested': True, 'event_type': 'user_input_request', 'prompt': '', 'team_conversation_id': 'c349277e-a6f3-44f2-8437-e0bd86b56ab7'}}, 'success': True, 'message': 'Human input requested by the team', 'final': False, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': 'global_agent', 'request_id': None, 'error_code': None, 'details': None}
2025-07-16 15:34:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:34:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:34:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:34:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-MergeDataComponent-*************', 'data': b'expired'}
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-MergeDataComponent-*************'
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-MergeDataComponent-*************
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Extracted key: result:transition-MergeDataComponent-*************
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-16 15:34:34 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-16 15:34:34 - RedisEventListener - INFO - Detected expired event for result of transition: transition-MergeDataComponent-*************
2025-07-16 15:34:34 - RedisEventListener - INFO - Archiving result for transition: transition-MergeDataComponent-*************
2025-07-16 15:34:34 - StateManager - DEBUG - Attempting to archive result for transition transition-MergeDataComponent-*************
2025-07-16 15:34:35 - StateManager - DEBUG - Provided result: False
2025-07-16 15:34:35 - StateManager - DEBUG - Trying to get result from Redis for transition transition-MergeDataComponent-*************
2025-07-16 15:34:36 - StateManager - DEBUG - No result found in Redis for transition transition-MergeDataComponent-*************
2025-07-16 15:34:36 - StateManager - DEBUG - Trying to get result from memory for transition transition-MergeDataComponent-*************
2025-07-16 15:34:36 - StateManager - DEBUG - Found result in memory for transition transition-MergeDataComponent-*************
2025-07-16 15:34:36 - StateManager - DEBUG - Archiving result to PostgreSQL for transition transition-MergeDataComponent-*************
2025-07-16 15:34:36 - PostgresManager - DEBUG - Attempting to store transition result for transition-MergeDataComponent-************* in correlation b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:34:36 - PostgresManager - DEBUG - Result data type: <class 'dict'>
2025-07-16 15:34:36 - PostgresManager - DEBUG - Result data: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}, 'status': 'completed', 'timestamp': 1752659973.497538}}
2025-07-16 15:34:39 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 15:34:39 - PostgresManager - DEBUG - Successfully serialized result data to JSON string
2025-07-16 15:34:39 - PostgresManager - DEBUG - Converted to psycopg2 Json object
2025-07-16 15:34:39 - PostgresManager - DEBUG - Inserting new record for transition transition-MergeDataComponent-*************
2025-07-16 15:34:40 - PostgresManager - DEBUG - Inserted new result for transition transition-MergeDataComponent-************* in correlation b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:34:40 - PostgresManager - DEBUG - Successfully stored transition result for transition-MergeDataComponent-*************
2025-07-16 15:34:40 - StateManager - INFO - Archived result for transition transition-MergeDataComponent-************* to PostgreSQL
2025-07-16 15:35:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:35:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:35:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:35:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:36:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:36:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:36:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:36:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:37:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:37:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:37:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:37:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:a247c23b-0630-43aa-a3d1-224af19adc8b', 'data': b'expired'}
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:a247c23b-0630-43aa-a3d1-224af19adc8b'
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:a247c23b-0630-43aa-a3d1-224af19adc8b
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Extracted key: workflow_state:a247c23b-0630-43aa-a3d1-224af19adc8b
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-16 15:37:42 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-16 15:37:42 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: a247c23b-0630-43aa-a3d1-224af19adc8b
2025-07-16 15:37:42 - RedisEventListener - INFO - Archiving workflow state for workflow: a247c23b-0630-43aa-a3d1-224af19adc8b
2025-07-16 15:37:46 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 15:37:46 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:37:47 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:38:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:38:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:38:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:38:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:39:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:39:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:39:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:39:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16', 'data': b'expired'}
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16'
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Extracted key: workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-16 15:39:35 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-16 15:39:35 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:39:35 - RedisEventListener - INFO - Archiving workflow state for workflow: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:39:39 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 15:39:39 - PostgresManager - DEBUG - Updated workflow state for correlation_id: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:39:40 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:40:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:40:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:40:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:40:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
^C2025-07-16 15:40:34 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
^C2025-07-16 15:40:35 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:40:35 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:40:35 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-07-16 15:40:35 - Main - ERROR - Shutting down due to keyboard interrupt...
^C%                                                                                                
prathamagarwal@Pratham-ka-MacBook-Air orchestration-engine % 
prathamagarwal@Pratham-ka-MacBook-Air orchestration-engine % ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Starting Orchestration Engine
2025-07-16 15:40:39 - Main - INFO - Starting Server
2025-07-16 15:40:39 - Main - INFO - Connection at: **************:9092
2025-07-16 15:40:39 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-07-16 15:40:39 - NodeExecutor - INFO - NodeExecutor initialized.
2025-07-16 15:40:39 - AgentExecutor - INFO - AgentExecutor initialized.
2025-07-16 15:40:39 - WorkflowExecutor - INFO - WorkflowExecutor initialized.
2025-07-16 15:40:39 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-07-16 15:40:39 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-16 15:40:41 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-16 15:40:41 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-16 15:40:42 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-16 15:40:44 - PostgresManager - INFO - PostgreSQL connection pool created
2025-07-16 15:40:44 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-07-16 15:40:47 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 15:40:48 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-07-16 15:40:48 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-07-16 15:40:49 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-07-16 15:40:49 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-07-16 15:40:51 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-07-16 15:40:51 - RedisEventListener - INFO - Starting Redis event listener thread
2025-07-16 15:40:51 - RedisEventListener - INFO - Redis event listener started
2025-07-16 15:40:51 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-07-16 15:40:51 - StateManager - DEBUG - Using provided database connections
2025-07-16 15:40:51 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:40:51 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:40:51 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:40:51 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-16 15:40:52 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:40:52 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:40:52 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-16 15:40:52 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-16 15:40:52 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-16 15:40:52 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-16 15:40:54 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-16 15:40:54 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-16 15:40:54 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-16 15:41:06 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-16 15:41:12 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-16 15:41:12 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-16 15:41:12 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-16 15:41:19 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-16 15:41:19 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-16 15:41:19 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-16 15:41:25 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-16 15:41:25 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-16 15:41:25 - WorkflowExecutor - INFO - WorkflowExecutor started successfully.
2025-07-16 15:41:25 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1368
2025-07-16 15:41:25 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1752660666, 'task_type': 'workflow', 'data': {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-07-16 15:41:25 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:41:25 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:41:26 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-16 15:41:26 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow testing_migration retrieved successfully",
  "workflow": {
    "id": "b33ceffe-e932-4e14-bdd1-fb9aeebccbb0",
    "name": "testing_migration",
    "description": "testing_migration",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/b12d9596-c1bc-4e52-b036-4106a02a9eca.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/51a32dc1-1e05-42be-86f3-e8b3cee909a0.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "dict",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-07-16T06:00:21.689664",
    "updated_at": "2025-07-16T10:10:53.551079",
    "available_nodes": [
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************",
        "label": "Merge Data"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************",
        "label": "AI Agent Executor"
      }
    ],
    "is_updated": true,
    "source_version_id": null
  }
}
2025-07-16 15:41:26 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b33ceffe-e932-4e14-bdd1-fb9aeebccbb0 - server_script_path is optional
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Skipping field 'main_input' for transition 'transition-MergeDataComponent-*************' (intended for 'MergeDataComponent-*************')
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'main_input' for transition 'transition-MergeDataComponent-*************'
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Target transition for field 'main_input': 'MergeDataComponent-*************'
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Target transition 'MergeDataComponent-*************' doesn't exist, using as fallback for 'transition-MergeDataComponent-*************'
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-MergeDataComponent-*************
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Checking transition transition-MergeDataComponent-*************: node_type='', is_conditional=False
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-MergeDataComponent-*************
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-AgenticAI-*************
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Checking transition transition-AgenticAI-*************: node_type='', is_conditional=False
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-AgenticAI-*************
2025-07-16 15:41:26 - app.services.initialize_workflow - DEBUG - Preserved payload structure in workflow: {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}
2025-07-16 15:41:26 - EnhancedWorkflowEngine - DEBUG - Stored user_payload_template: {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}
2025-07-16 15:41:26 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-16 15:41:26 - StateManager - DEBUG - Using global database connections from initializer
2025-07-16 15:41:26 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:41:26 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:41:26 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:41:27 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:41:27 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:41:27 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-16 15:41:27 - StateManager - DEBUG - Using provided database connections
2025-07-16 15:41:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:41:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:41:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:41:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:41:28 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:41:28 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-MergeDataComponent-*************']
2025-07-16 15:41:28 - StateManager - INFO - Built dependency map for 2 transitions
2025-07-16 15:41:28 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-MergeDataComponent-*************']
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-AgenticAI-*************
2025-07-16 15:41:28 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:41:28 - StateManager - INFO - Set end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:41:28 - MCPToolExecutor - DEBUG - Set correlation ID to: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 in tool_executor
2025-07-16 15:41:28 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-07-16 15:41:28 - NodeExecutor - DEBUG - Set correlation ID to: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 in node_executor
2025-07-16 15:41:28 - AgentExecutor - DEBUG - Set correlation ID to: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 in agent_executor
2025-07-16 15:41:28 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:41:28 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-07-16 15:41:28 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-16 15:41:28 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:28 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:28 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-16 15:41:28 - StateManager - INFO - Workflow initialized with initial transition: transition-MergeDataComponent-*************
2025-07-16 15:41:28 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************'}, waiting=set(), completed=set()
2025-07-16 15:41:28 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-MergeDataComponent-*************
2025-07-16 15:41:28 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-07-16 15:41:28 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:37dea7d5-8ea5-4a84-a10a-c0b54777d3d6'
2025-07-16 15:41:29 - RedisManager - DEBUG - Set key 'workflow_state:37dea7d5-8ea5-4a84-a10a-c0b54777d3d6' with TTL of 600 seconds
2025-07-16 15:41:29 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:41:29 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:41:29 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:41:29 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-07-16 15:41:29 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:41:29 - StateManager - INFO - Terminated: False
2025-07-16 15:41:29 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:41:29 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:41:29 - StateManager - INFO - Completed transitions (0): []
2025-07-16 15:41:29 - StateManager - INFO - Results stored for 0 transitions
2025-07-16 15:41:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:41:29 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:41:29 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:41:29 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:41:29 - StateManager - INFO - Workflow paused: False
2025-07-16 15:41:29 - StateManager - INFO - ==============================
2025-07-16 15:41:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-16 15:41:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-16 15:41:29 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-16 15:41:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-16 15:41:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-16 15:41:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-16 15:41:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-16 15:41:29 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:41:29 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-16 15:41:29 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 3 fields (21 null/empty fields removed)
2025-07-16 15:41:29 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:41:29 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:41:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-16 15:41:29 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 77ecce5c-be2e-4889-a14a-843567c84c3d) using provided producer.
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Added correlation_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 to payload
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}, 'request_id': '77ecce5c-be2e-4889-a14a-843567c84c3d', 'correlation_id': '37dea7d5-8ea5-4a84-a10a-c0b54777d3d6', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Request 77ecce5c-be2e-4889-a14a-843567c84c3d sent successfully using provided producer.
2025-07-16 15:41:29 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 77ecce5c-be2e-4889-a14a-843567c84c3d...
2025-07-16 15:41:29 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1368, corr_id: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6
2025-07-16 15:41:30 - NodeExecutor - DEBUG - Result consumer received message: Offset=1415
2025-07-16 15:41:30 - NodeExecutor - DEBUG - Received valid result for request_id 77ecce5c-be2e-4889-a14a-843567c84c3d
2025-07-16 15:41:30 - NodeExecutor - INFO - Result received for request 77ecce5c-be2e-4889-a14a-843567c84c3d.
2025-07-16 15:41:30 - TransitionHandler - INFO - Execution result from Components executor: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:41:30 - TransitionHandler - INFO - Checking execution result for errors: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:41:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'raw_result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'approval_required': False}
2025-07-16 15:41:30 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}, 'status': 'completed', 'timestamp': 1752660690.326026}}
2025-07-16 15:41:31 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-16 15:41:31 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-16 15:41:31 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:41:31 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-16 15:41:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['email_1']
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.15 seconds
2025-07-16 15:41:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'result': 'Completed transition in 2.15 seconds', 'message': 'Transition completed in 2.15 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-16 15:41:31 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************']]
2025-07-16 15:41:31 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-16 15:41:31 - EnhancedWorkflowEngine - INFO - Transition transition-MergeDataComponent-************* completed successfully: 1 next transitions
2025-07-16 15:41:31 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************']
2025-07-16 15:41:31 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-16 15:41:31 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-16 15:41:31 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:37dea7d5-8ea5-4a84-a10a-c0b54777d3d6'
2025-07-16 15:41:32 - RedisManager - DEBUG - Set key 'workflow_state:37dea7d5-8ea5-4a84-a10a-c0b54777d3d6' with TTL of 600 seconds
2025-07-16 15:41:32 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:41:32 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:41:32 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:41:32 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:41:32 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:41:32 - StateManager - INFO - Terminated: False
2025-07-16 15:41:32 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:41:32 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:41:32 - StateManager - INFO - Completed transitions (1): ['transition-MergeDataComponent-*************']
2025-07-16 15:41:32 - StateManager - INFO - Results stored for 1 transitions
2025-07-16 15:41:32 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:41:32 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:41:32 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:41:32 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:41:32 - StateManager - INFO - Workflow paused: False
2025-07-16 15:41:32 - StateManager - INFO - ==============================
2025-07-16 15:41:32 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-16 15:41:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-16 15:41:32 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-16 15:41:32 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=agent, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:41:32 - StateManager - DEBUG - Retrieved result for transition transition-MergeDataComponent-************* from Redis
2025-07-16 15:41:32 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MergeDataComponent-*************, extracting data
2025-07-16 15:41:32 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MergeDataComponent-*************
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MergeDataComponent-*************
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MergeDataComponent-************* (total: 1)
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:41:32 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-16 15:41:32 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Looking for results for transition transition-MergeDataComponent-*************, iteration_context: False
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Base ID transition-MergeDataComponent-************* results: found
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → input_variables via path 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:41:32 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-16 15:41:32 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Preserving field 'query' with agent-platform template variables: You are an email scoring specialist. Analyze multiple emails and return the best one based on scorin...
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Filtering out field 'input' with null/empty value: 
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with null/empty value: None
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-16 15:41:32 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-16 15:41:32 - WorkflowUtils - INFO - 🧹 Parameter filtering: 8 → 6 fields (2 null/empty fields removed)
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📌 Using top-level system_message: Analyze multiple emails and return the best one based on scoring criteria
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}
2025-07-16 15:41:32 - TransitionHandler - DEBUG - 📌 Added static parameter: variables = {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}
2025-07-16 15:41:32 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:41:32 - TransitionHandler - DEBUG - tool Parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:41:32 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:41:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6):
2025-07-16 15:41:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, response: {'transition_id': 'transition-AgenticAI-*************', 'node_label': 'AI Agent Executor', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AI Agent Executor', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-16 15:41:32 - AgentExecutor - DEBUG - Component agent: query='None', input='None', final_query='None'
2025-07-16 15:41:32 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: a618ed43-8678-4752-9900-e21c6a872da8) with correlation_id: 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6, user_id: c1454e90-09ac-40f2-bde2-833387d7b645 using provided producer.
2025-07-16 15:41:32 - AgentExecutor - INFO - Building component agent request for execution_type: response
agent config in build component agent request:  {'id': '4a33afbe-e728-4aee-8706-7977ba6815ac', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}
system_message in build component agent request:  Analyze multiple emails and return the best one based on scoring criteria
input_variables in build component agent request:  {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:41:32 - AgentExecutor - DEBUG - Added correlation_id 37dea7d5-8ea5-4a84-a10a-c0b54777d3d6 to payload
2025-07-16 15:41:32 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'a618ed43-8678-4752-9900-e21c6a872da8', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'correlation_id': '37dea7d5-8ea5-4a84-a10a-c0b54777d3d6', 'agent_type': 'component', 'execution_type': 'response', 'query': None, 'variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'agent_config': {'id': '4a33afbe-e728-4aee-8706-7977ba6815ac', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-16 15:41:32 - AgentExecutor - DEBUG - Request a618ed43-8678-4752-9900-e21c6a872da8 sent successfully using provided producer.
2025-07-16 15:41:32 - AgentExecutor - DEBUG - Waiting for single response result for request a618ed43-8678-4752-9900-e21c6a872da8...
2025-07-16 15:41:48 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:41:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:41:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-07-16 15:41:48 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
