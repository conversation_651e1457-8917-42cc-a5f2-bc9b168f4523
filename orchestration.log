stener
2025-07-16 13:42:01 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 13:42:01 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 13:42:01 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-MergeDataComponent-*************']
2025-07-16 13:42:01 - StateManager - INFO - Built dependency map for 2 transitions
2025-07-16 13:42:01 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-MergeDataComponent-*************']
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-AgenticAI-*************
2025-07-16 13:42:01 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-AgenticAI-*************'}
2025-07-16 13:42:01 - State<PERSON>anager - INFO - Set end transitions: {'transition-AgenticAI-*************'}
2025-07-16 13:42:01 - MCPToolExecutor - DEBUG - Set correlation ID to: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5 in tool_executor
2025-07-16 13:42:01 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-07-16 13:42:01 - NodeExecutor - DEBUG - Set correlation ID to: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5 in node_executor
2025-07-16 13:42:01 - AgentExecutor - DEBUG - Set correlation ID to: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5 in agent_executor
2025-07-16 13:42:01 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 13:42:01 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-07-16 13:42:01 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-16 13:42:01 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:01 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:01 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-16 13:42:01 - StateManager - INFO - Workflow initialized with initial transition: transition-MergeDataComponent-*************
2025-07-16 13:42:01 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************'}, waiting=set(), completed=set()
2025-07-16 13:42:01 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-MergeDataComponent-*************
2025-07-16 13:42:01 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-07-16 13:42:02 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:d8480ffc-b1b3-4de3-aa0e-72cf43b056e5'
2025-07-16 13:42:03 - RedisManager - DEBUG - Set key 'workflow_state:d8480ffc-b1b3-4de3-aa0e-72cf43b056e5' with TTL of 600 seconds
2025-07-16 13:42:03 - StateManager - INFO - Workflow state saved to Redis for workflow ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 13:42:03 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 13:42:03 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 13:42:03 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-07-16 13:42:03 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 13:42:03 - StateManager - INFO - Terminated: False
2025-07-16 13:42:03 - StateManager - INFO - Pending transitions (0): []
2025-07-16 13:42:03 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 13:42:03 - StateManager - INFO - Completed transitions (0): []
2025-07-16 13:42:03 - StateManager - INFO - Results stored for 0 transitions
2025-07-16 13:42:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 13:42:03 - StateManager - INFO - Workflow status: inactive
2025-07-16 13:42:03 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 13:42:03 - StateManager - INFO - Workflow status: inactive
2025-07-16 13:42:03 - StateManager - INFO - Workflow paused: False
2025-07-16 13:42:03 - StateManager - INFO - ==============================
2025-07-16 13:42:03 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-16 13:42:03 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-16 13:42:03 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-16 13:42:03 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-16 13:42:03 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-16 13:42:03 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-16 13:42:03 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 13:42:03 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-16 13:42:03 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 3 fields (21 null/empty fields removed)
2025-07-16 13:42:03 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 13:42:03 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-16 13:42:03 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 90f72795-dae8-4020-98db-580cb20d54b0) using provided producer.
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Added correlation_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5 to payload
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}, 'request_id': '90f72795-dae8-4020-98db-580cb20d54b0', 'correlation_id': 'd8480ffc-b1b3-4de3-aa0e-72cf43b056e5', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Request 90f72795-dae8-4020-98db-580cb20d54b0 sent successfully using provided producer.
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 90f72795-dae8-4020-98db-580cb20d54b0...
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1359, corr_id: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Result consumer received message: Offset=1406
2025-07-16 13:42:03 - NodeExecutor - DEBUG - Received valid result for request_id 90f72795-dae8-4020-98db-580cb20d54b0
2025-07-16 13:42:03 - NodeExecutor - INFO - Result received for request 90f72795-dae8-4020-98db-580cb20d54b0.
2025-07-16 13:42:03 - TransitionHandler - INFO - Execution result from Components executor: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here's what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs \"earnest founders solving unsexy problems with exponential tech\"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC's mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere's a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI's customer support automation round. What caught my eye wasn't just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the \"unsexy\" enterprise workflows.\n\nWe're seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here's what's interesting: while Yuma focuses on customer support, we're building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I'm Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We've helped 500+ innovators compress months into days. Now with RUH.AI, we're tackling what I call the \"Daemon problem\" (inspired by Suarez's book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about \"founder-market-earnestness fit\" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I'm building this because I've felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 13:42:03 - TransitionHandler - INFO - Checking execution result for errors: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here's what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs \"earnest founders solving unsexy problems with exponential tech\"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC's mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere's a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI's customer support automation round. What caught my eye wasn't just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the \"unsexy\" enterprise workflows.\n\nWe're seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here's what's interesting: while Yuma focuses on customer support, we're building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I'm Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We've helped 500+ innovators compress months into days. Now with RUH.AI, we're tackling what I call the \"Daemon problem\" (inspired by Suarez's book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about \"founder-market-earnestness fit\" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I'm building this because I've felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:03 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'raw_result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'approval_required': False}
2025-07-16 13:42:03 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}}, 'status': 'completed', 'timestamp': 1752653523.7011971}}
2025-07-16 13:42:04 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-16 13:42:04 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-16 13:42:04 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 13:42:04 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-16 13:42:04 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['email_1']
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 1.39 seconds
2025-07-16 13:42:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'result': 'Completed transition in 1.39 seconds', 'message': 'Transition completed in 1.39 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-16 13:42:04 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************']]
2025-07-16 13:42:04 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-16 13:42:04 - EnhancedWorkflowEngine - INFO - Transition transition-MergeDataComponent-************* completed successfully: 1 next transitions
2025-07-16 13:42:04 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************']
2025-07-16 13:42:04 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-16 13:42:04 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-16 13:42:04 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:d8480ffc-b1b3-4de3-aa0e-72cf43b056e5'
2025-07-16 13:42:05 - RedisManager - DEBUG - Set key 'workflow_state:d8480ffc-b1b3-4de3-aa0e-72cf43b056e5' with TTL of 600 seconds
2025-07-16 13:42:05 - StateManager - INFO - Workflow state saved to Redis for workflow ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 13:42:05 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 13:42:05 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 13:42:05 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-16 13:42:05 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 13:42:05 - StateManager - INFO - Terminated: False
2025-07-16 13:42:05 - StateManager - INFO - Pending transitions (0): []
2025-07-16 13:42:05 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 13:42:05 - StateManager - INFO - Completed transitions (1): ['transition-MergeDataComponent-*************']
2025-07-16 13:42:05 - StateManager - INFO - Results stored for 1 transitions
2025-07-16 13:42:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 13:42:05 - StateManager - INFO - Workflow status: inactive
2025-07-16 13:42:05 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 13:42:05 - StateManager - INFO - Workflow status: inactive
2025-07-16 13:42:05 - StateManager - INFO - Workflow paused: False
2025-07-16 13:42:05 - StateManager - INFO - ==============================
2025-07-16 13:42:05 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-16 13:42:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-16 13:42:05 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-16 13:42:05 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=agent, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 13:42:05 - StateManager - DEBUG - Retrieved result for transition transition-MergeDataComponent-************* from Redis
2025-07-16 13:42:05 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MergeDataComponent-*************, extracting data
2025-07-16 13:42:05 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MergeDataComponent-*************
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MergeDataComponent-*************
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MergeDataComponent-************* (total: 1)
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}}
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Found result.result: {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'} (type: <class 'dict'>)
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 13:42:05 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-16 13:42:05 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Looking for results for transition transition-MergeDataComponent-*************, iteration_context: False
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Base ID transition-MergeDataComponent-************* results: found
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}}
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Found result.result: {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'} (type: <class 'dict'>)
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → input_variables via path 'result': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}
2025-07-16 13:42:05 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-16 13:42:05 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Filtering out field 'input' with null/empty value: 
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with null/empty value: None
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-16 13:42:05 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-16 13:42:05 - WorkflowUtils - INFO - 🧹 Parameter filtering: 8 → 6 fields (2 null/empty fields removed)
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📌 Using top-level system_message: Analyze multiple emails and return the best one based on scoring criteria
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}
2025-07-16 13:42:05 - TransitionHandler - DEBUG - 📌 Added static parameter: variables = {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}
2025-07-16 13:42:05 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_variables': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 13:42:05 - TransitionHandler - DEBUG - tool Parameters: {'input_variables': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 13:42:05 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'input_variables': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 13:42:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5):
2025-07-16 13:42:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, response: {'transition_id': 'transition-AgenticAI-*************', 'node_label': 'AI Agent Executor', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AI Agent Executor', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-16 13:42:05 - AgentExecutor - DEBUG - Component agent: query='None', input='None', final_query='None'
2025-07-16 13:42:05 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: 7176a2f4-3e4a-4f3a-b4b3-48b7ac1b615e) with correlation_id: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5, user_id: c1454e90-09ac-40f2-bde2-833387d7b645 using provided producer.
2025-07-16 13:42:05 - AgentExecutor - INFO - Building component agent request for execution_type: response
agent config in build component agent request:  {'id': '0e7d60e4-e4ad-4643-81c8-65698e6e1d7d', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}
system_message in build component agent request:  Analyze multiple emails and return the best one based on scoring criteria
input_variables in build component agent request:  {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}
2025-07-16 13:42:05 - AgentExecutor - DEBUG - Added correlation_id d8480ffc-b1b3-4de3-aa0e-72cf43b056e5 to payload
2025-07-16 13:42:05 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': '7176a2f4-3e4a-4f3a-b4b3-48b7ac1b615e', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'correlation_id': 'd8480ffc-b1b3-4de3-aa0e-72cf43b056e5', 'agent_type': 'component', 'execution_type': 'response', 'query': None, 'variables': {'email_1': 'I\'ll analyze Garry Tan\'s investment activities and create a personalized email from Jesse Anglen.\n\nBased on the research data, here\'s what I found about Garry Tan:\n\n**Recent Investment Activity (2024):**\n- Made 8 investments in Q4 2024, with 75% being pre-seed rounds\n- Strong focus on AI/ML applications (62.5% of investments)\n- Notable investments include Zip ($190M Series D), Yuma AI ($5M Seed), and several AI startups\n- Consistent $500K checks for pre-seed rounds\n\n**Investment Philosophy:**\n- Backs "earnest founders solving unsexy problems with exponential tech"\n- Prioritizes founder-market-earnestness fit over metrics or pedigrees\n- Focus on pre-product-market-fit startups through YC\'s mentorship model\n\n**Key Context:**\n- President and CEO of Y Combinator since January 2023\n- Co-founded Initialized Capital (recent restructuring in Oct 2024)\n- Reviewed 6,000+ YC applications, advised 35+ unicorns worth $226B\n- Strong advocate for AI infrastructure and enterprise automation\n\nHere\'s a personalized email from Jesse to Garry:\n\n---\n\n**Subject:** Quick question on your Yuma AI thesis - seeing similar patterns at RUH.AI\n\nHey Garry,\n\nI noticed your recent investment in Yuma AI\'s customer support automation round. What caught my eye wasn\'t just the $5M seed (congrats to them), but your broader pattern of backing AI infrastructure plays that handle the "unsexy" enterprise workflows.\n\nWe\'re seeing something similar at RUH.AI - just automated 300 jobs in 30 days for one client, achieving 75% reduction in busywork. But here\'s what\'s interesting: while Yuma focuses on customer support, we\'re building a more horizontal play with our Ruh-R1-14B model that adapts across departments.\n\nQuick context - I\'m Jesse from Post Falls, Idaho. Spent 10 years in construction/real estate before diving into blockchain, then founded Rapid Innovation in 2019. We\'ve helped 500+ innovators compress months into days. Now with RUH.AI, we\'re tackling what I call the "Daemon problem" (inspired by Suarez\'s book) - creating truly autonomous AI agents that make humans increasingly unnecessary in repetitive tasks.\n\nI saw your comment about "founder-market-earnestness fit" being your primary criterion. Well, I learned business through the School of Hard Knocks, not Stanford, and I\'m building this because I\'ve felt the pain of scaling operations firsthand.\n\nWould love to get your take on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who\'s seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital\'s restructuring'}, 'agent_config': {'id': '0e7d60e4-e4ad-4643-81c8-65698e6e1d7d', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-16 13:42:05 - AgentExecutor - DEBUG - Request 7176a2f4-3e4a-4f3a-b4b3-48b7ac1b615e sent successfully using provided producer.
2025-07-16 13:42:05 - AgentExecutor - DEBUG - Waiting for single response result for request 7176a2f4-3e4a-4f3a-b4b3-48b7ac1b615e...
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Raw state DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@6__:*', 'channel': b'__keyspace@6__:workflow_state:8d8e702e-de6b-4b08-9de9-950f6b058144', 'data': b'expired'}
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@6__:workflow_state:8d8e702e-de6b-4b08-9de9-950f6b058144'
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Decoded channel: __keyspace@6__:workflow_state:8d8e702e-de6b-4b08-9de9-950f6b058144
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Extracted key: workflow_state:8d8e702e-de6b-4b08-9de9-950f6b058144
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-07-16 13:42:55 - RedisEventListener - DEBUG - Decoded event: expired
2025-07-16 13:42:55 - RedisEventListener - INFO - Detected expired event for workflow state of workflow: 8d8e702e-de6b-4b08-9de9-950f6b058144
2025-07-16 13:42:55 - RedisEventListener - INFO - Archiving workflow state for workflow: 8d8e702e-de6b-4b08-9de9-950f6b058144
2025-07-16 13:42:59 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-07-16 13:43:00 - PostgresManager - DEBUG - Inserted new workflow state for correlation_id: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:43:01 - StateManager - INFO - Archived workflow state to PostgreSQL for workflow ID: d8480ffc-b1b3-4de3-aa0e-72cf43b056e5
2025-07-16 13:43:01 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
