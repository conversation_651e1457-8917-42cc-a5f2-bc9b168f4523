2025-07-16 15:28:35 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:28:35 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:28:36 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-07-16 15:28:36 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:28:36 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:28:36 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-07-16 15:28:36 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-07-16 15:28:36 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-07-16 15:28:36 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-07-16 15:28:39 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-07-16 15:28:39 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-07-16 15:28:39 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-07-16 15:28:48 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-07-16 15:28:54 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-07-16 15:28:54 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-07-16 15:28:55 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-07-16 15:29:01 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-07-16 15:29:01 - NodeExecutor - INFO - Background result consumer loop started.
2025-07-16 15:29:01 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-07-16 15:29:07 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-07-16 15:29:07 - AgentExecutor - INFO - Background result consumer loop started.
2025-07-16 15:29:07 - WorkflowExecutor - INFO - WorkflowExecutor started successfully.
2025-07-16 15:29:12 - AgentExecutor - DEBUG - Result consumer received message: Offset=26848
2025-07-16 15:29:12 - AgentExecutor - DEBUG - Processing result payload: {'run_id': '086db782-6d23-4b56-9c73-bfa8e06b1cb8', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'session_initialized', 'success': True, 'message': 'Orchestration team session created successfully'}
2025-07-16 15:29:12 - AgentExecutor - DEBUG - Agent response extracted: None
2025-07-16 15:29:12 - AgentExecutor - ERROR - Agent response is None despite success=True. Full payload: {'run_id': '086db782-6d23-4b56-9c73-bfa8e06b1cb8', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'session_initialized', 'success': True, 'message': 'Orchestration team session created successfully'}
2025-07-16 15:29:12 - AgentExecutor - WARNING - Received result message without 'request_id': {'run_id': '086db782-6d23-4b56-9c73-bfa8e06b1cb8', 'session_id': '932ebd80-8adb-439a-9e1d-79dc163c78cb', 'event_type': 'session_initialized', 'success': True, 'message': 'Orchestration team session created successfully'}
2025-07-16 15:29:29 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=1367
2025-07-16 15:29:29 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1752659968, 'task_type': 'workflow', 'data': {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'payload': {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}, 'approval': True, 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645'}, 'approval': True}
2025-07-16 15:29:29 - KafkaWorkflowConsumer - INFO - Extracted user_id: c1454e90-09ac-40f2-bde2-833387d7b645 for workflow: b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:29:29 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b33ceffe-e932-4e14-bdd1-fb9aeebccbb0
2025-07-16 15:29:29 - WorkflowService - DEBUG - Received response with status code: 200
2025-07-16 15:29:29 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow testing_migration retrieved successfully",
  "workflow": {
    "id": "b33ceffe-e932-4e14-bdd1-fb9aeebccbb0",
    "name": "testing_migration",
    "description": "testing_migration",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/411281c5-37b0-4d8f-bc3f-6f5a88e8043e.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/ee949df7-6c6c-4c1c-b549-dcf493b8a2a6.json",
    "start_nodes": [
      {
        "field": "main_input",
        "type": "dict",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645",
    "user_ids": [
      "c1454e90-09ac-40f2-bde2-833387d7b645"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-07-16T06:00:21.689664",
    "updated_at": "2025-07-16T09:40:23.366862",
    "available_nodes": [
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************",
        "label": "Merge Data"
      },
      {
        "name": "AgenticAI",
        "display_name": "AI Agent Executor",
        "type": "agent",
        "transition_id": "transition-AgenticAI-*************",
        "label": "AI Agent Executor"
      }
    ],
    "is_updated": true,
    "source_version_id": null
  }
}
2025-07-16 15:29:29 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b33ceffe-e932-4e14-bdd1-fb9aeebccbb0 - server_script_path is optional
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Skipping field 'main_input' for transition 'transition-MergeDataComponent-*************' (intended for 'MergeDataComponent-*************')
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Processing user-dependent field 'main_input' for transition 'transition-MergeDataComponent-*************'
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Target transition for field 'main_input': 'MergeDataComponent-*************'
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Target transition 'MergeDataComponent-*************' doesn't exist, using as fallback for 'transition-MergeDataComponent-*************'
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-MergeDataComponent-*************
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Checking transition transition-MergeDataComponent-*************: node_type='', is_conditional=False
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-MergeDataComponent-*************
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Calling _inject_user_input_for_conditional_components for transition-AgenticAI-*************
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Checking transition transition-AgenticAI-*************: node_type='', is_conditional=False
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Skipping non-conditional transition transition-AgenticAI-*************
2025-07-16 15:29:29 - app.services.initialize_workflow - DEBUG - Preserved payload structure in workflow: {'user_dependent_fields': ['main_input'], 'user_payload_template': {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}}
2025-07-16 15:29:29 - EnhancedWorkflowEngine - DEBUG - Stored user_payload_template: {'main_input': {'value': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'transition_id': 'MergeDataComponent-*************'}}
2025-07-16 15:29:29 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-07-16 15:29:29 - StateManager - DEBUG - Using global database connections from initializer
2025-07-16 15:29:29 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:29:29 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:29:29 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:29:30 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:29:30 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:29:30 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-07-16 15:29:30 - StateManager - DEBUG - Using provided database connections
2025-07-16 15:29:30 - RedisEventListener - INFO - Workflow state manager reference updated
2025-07-16 15:29:30 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-07-16 15:29:30 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-07-16 15:29:31 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-07-16 15:29:31 - StateManager - INFO - WorkflowStateManager initialized
2025-07-16 15:29:31 - StateManager - DEBUG - Extracted dependencies for transition transition-AgenticAI-*************: ['transition-MergeDataComponent-*************']
2025-07-16 15:29:31 - StateManager - INFO - Built dependency map for 2 transitions
2025-07-16 15:29:31 - StateManager - DEBUG - Transition transition-AgenticAI-************* depends on: ['transition-MergeDataComponent-*************']
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Found end transition: transition-AgenticAI-*************
2025-07-16 15:29:31 - EnhancedWorkflowEngine - INFO - Found 1 end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:29:31 - StateManager - INFO - Set end transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:29:31 - MCPToolExecutor - DEBUG - Set correlation ID to: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b107939f-b9ef-4712-9c16-61e78fb9df16 in tool_executor
2025-07-16 15:29:31 - MCPToolExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in tool_executor
2025-07-16 15:29:31 - NodeExecutor - DEBUG - Set correlation ID to: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b107939f-b9ef-4712-9c16-61e78fb9df16 in node_executor
2025-07-16 15:29:31 - AgentExecutor - DEBUG - Set correlation ID to: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Set correlation_id b107939f-b9ef-4712-9c16-61e78fb9df16 in agent_executor
2025-07-16 15:29:31 - AgentExecutor - DEBUG - Set user ID to: c1454e90-09ac-40f2-bde2-833387d7b645
2025-07-16 15:29:31 - EnhancedWorkflowEngine - DEBUG - Set user_id c1454e90-09ac-40f2-bde2-833387d7b645 in agent_executor
2025-07-16 15:29:31 - TransitionHandler - INFO - TransitionHandler initialized
2025-07-16 15:29:31 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:31 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'workflow_id': 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0', 'status': 'Initialized', 'message': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-07-16 15:29:31 - StateManager - INFO - Workflow initialized with initial transition: transition-MergeDataComponent-*************
2025-07-16 15:29:31 - StateManager - DEBUG - State: pending={'transition-MergeDataComponent-*************'}, waiting=set(), completed=set()
2025-07-16 15:29:31 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-MergeDataComponent-*************
2025-07-16 15:29:31 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-07-16 15:29:32 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16'
2025-07-16 15:29:32 - RedisManager - DEBUG - Set key 'workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16' with TTL of 600 seconds
2025-07-16 15:29:32 - StateManager - INFO - Workflow state saved to Redis for workflow ID: b107939f-b9ef-4712-9c16-61e78fb9df16. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:29:32 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:29:32 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:29:32 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-07-16 15:29:32 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:29:32 - StateManager - INFO - Terminated: False
2025-07-16 15:29:32 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:29:32 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:29:32 - StateManager - INFO - Completed transitions (0): []
2025-07-16 15:29:32 - StateManager - INFO - Results stored for 0 transitions
2025-07-16 15:29:32 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:29:32 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:29:32 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:29:32 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:29:32 - StateManager - INFO - Workflow paused: False
2025-07-16 15:29:32 - StateManager - INFO - ==============================
2025-07-16 15:29:32 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-07-16 15:29:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'message': 'Starting execution...', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-07-16 15:29:32 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=initial, execution_type=Components)
2025-07-16 15:29:32 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-07-16 15:29:32 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-07-16 15:29:32 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-07-16 15:29:32 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-07-16 15:29:32 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=component, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:29:32 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-07-16 15:29:32 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 3 fields (21 null/empty fields removed)
2025-07-16 15:29:32 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:29:32 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}
2025-07-16 15:29:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Connecting to server', 'result': 'Connecting to server Merge Data', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-07-16 15:29:32 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: 087dbb12-4bdc-48b9-a9d4-436e87f2c8fc) using provided producer.
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Added correlation_id b107939f-b9ef-4712-9c16-61e78fb9df16 to payload
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Added transition_id transition-MergeDataComponent-************* to payload
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Added node_label Merge Data to payload
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'num_additional_inputs': 0, 'merge_strategy': 'Overwrite'}, 'request_id': '087dbb12-4bdc-48b9-a9d4-436e87f2c8fc', 'correlation_id': 'b107939f-b9ef-4712-9c16-61e78fb9df16', 'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data'}
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Request 087dbb12-4bdc-48b9-a9d4-436e87f2c8fc sent successfully using provided producer.
2025-07-16 15:29:32 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 087dbb12-4bdc-48b9-a9d4-436e87f2c8fc...
2025-07-16 15:29:32 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 1367, corr_id: b107939f-b9ef-4712-9c16-61e78fb9df16
2025-07-16 15:29:32 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-07-16 15:29:32 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-07-16 15:29:33 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at 34.172.106.233:6379, response: [b'pong', b'']
2025-07-16 15:29:33 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-07-16 15:29:33 - NodeExecutor - DEBUG - Result consumer received message: Offset=1414
2025-07-16 15:29:33 - NodeExecutor - DEBUG - Received valid result for request_id 087dbb12-4bdc-48b9-a9d4-436e87f2c8fc
2025-07-16 15:29:33 - NodeExecutor - INFO - Result received for request 087dbb12-4bdc-48b9-a9d4-436e87f2c8fc.
2025-07-16 15:29:33 - TransitionHandler - INFO - Execution result from Components executor: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:29:33 - TransitionHandler - INFO - Checking execution result for errors: {
  "email_1": "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"
}
2025-07-16 15:29:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'message': 'Transition Result received.', 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'status': 'completed', 'sequence': 2, 'workflow_status': 'running', 'raw_result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'approval_required': False}
2025-07-16 15:29:33 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_label': 'Merge Data', 'tool_name': 'MergeDataComponent', 'result': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}, 'status': 'completed', 'timestamp': 1752659973.497538}}
2025-07-16 15:29:34 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-07-16 15:29:34 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-07-16 15:29:34 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:29:34 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-07-16 15:29:34 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'transition-MergeDataComponent-*************'}
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['email_1']
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 Processing output_data for transition-MergeDataComponent-*************
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 output_data_configs count: 1
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 transition_output_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 chosen_next_transitions: []
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 conditional_nodes: set()
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 Added output transition: transition-AgenticAI-*************
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔗 Final next_transitions: ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.16 seconds
2025-07-16 15:29:34 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:34 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'result': 'Completed transition in 2.16 seconds', 'message': 'Transition completed in 2.16 seconds', 'transition_id': 'transition-MergeDataComponent-*************', 'status': 'time_logged', 'sequence': 3, 'workflow_status': 'running'}
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔄 _execute_transition_with_tracking returning for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - TransitionHandler - DEBUG - 🔄 Return type: <class 'list'>, length: 1
2025-07-16 15:29:34 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-AgenticAI-*************']]
2025-07-16 15:29:34 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-MergeDataComponent-*************: ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'list'>, is_list: True
2025-07-16 15:29:34 - EnhancedWorkflowEngine - INFO - Transition transition-MergeDataComponent-************* completed successfully: 1 next transitions
2025-07-16 15:29:34 - EnhancedWorkflowEngine - DEBUG - 🔄 Added to next_transitions_to_execute: ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-AgenticAI-*************']
2025-07-16 15:29:34 - EnhancedWorkflowEngine - INFO - Adding transition transition-AgenticAI-************* to pending (all dependencies met)
2025-07-16 15:29:34 - StateManager - DEBUG - Workflow active: {'transition-AgenticAI-*************'}
2025-07-16 15:29:35 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16'
2025-07-16 15:29:35 - RedisManager - DEBUG - Set key 'workflow_state:b107939f-b9ef-4712-9c16-61e78fb9df16' with TTL of 600 seconds
2025-07-16 15:29:35 - StateManager - INFO - Workflow state saved to Redis for workflow ID: b107939f-b9ef-4712-9c16-61e78fb9df16. Will be archived to PostgreSQL when Redis key expires.
2025-07-16 15:29:35 - StateManager - DEBUG - Checking waiting transitions: set()
2025-07-16 15:29:35 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-07-16 15:29:35 - StateManager - INFO - Cleared 1 pending transitions: {'transition-AgenticAI-*************'}
2025-07-16 15:29:35 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-07-16 15:29:35 - StateManager - INFO - Terminated: False
2025-07-16 15:29:35 - StateManager - INFO - Pending transitions (0): []
2025-07-16 15:29:35 - StateManager - INFO - Waiting transitions (0): []
2025-07-16 15:29:35 - StateManager - INFO - Completed transitions (1): ['transition-MergeDataComponent-*************']
2025-07-16 15:29:35 - StateManager - INFO - Results stored for 1 transitions
2025-07-16 15:29:35 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:29:35 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:29:35 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-07-16 15:29:35 - StateManager - INFO - Workflow status: inactive
2025-07-16 15:29:35 - StateManager - INFO - Workflow paused: False
2025-07-16 15:29:35 - StateManager - INFO - ==============================
2025-07-16 15:29:35 - TransitionHandler - INFO - Starting parallel execution of transition: transition-AgenticAI-*************
2025-07-16 15:29:35 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:35 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'result': 'Starting execution of transition: transition-AgenticAI-*************', 'message': 'Starting execution...', 'transition_id': 'transition-AgenticAI-*************', 'status': 'started', 'sequence': 4, 'workflow_status': 'running'}
2025-07-16 15:29:35 - TransitionHandler - EXECUTE - Transition 'transition-AgenticAI-*************' (type=standard, execution_type=agent)
2025-07-16 15:29:35 - TransitionHandler - INFO - Using AgentExecutor for execution_type: agent
2025-07-16 15:29:35 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-AgenticAI-*************
2025-07-16 15:29:35 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: agent
2025-07-16 15:29:35 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for agent node
2025-07-16 15:29:35 - TransitionHandler - DEBUG - 🔀 Conditional detection: node_type=agent, is_conditional_node_type=False, is_conditional_transition_id=False
2025-07-16 15:29:36 - StateManager - DEBUG - Retrieved result for transition transition-MergeDataComponent-************* from Redis
2025-07-16 15:29:36 - StateManager - DEBUG - Detected wrapped result structure for transition transition-MergeDataComponent-*************, extracting data
2025-07-16 15:29:36 - StateManager - DEBUG - Extracted double-nested result data for transition transition-MergeDataComponent-*************
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-MergeDataComponent-*************
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-MergeDataComponent-************* (total: 1)
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:29:36 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-07-16 15:29:36 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Looking for results for transition transition-MergeDataComponent-*************, iteration_context: False
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Base ID transition-MergeDataComponent-************* results: found
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'output_data': {'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}}
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['result']
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Path tracking - Found single-nested result for handle output_data: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Found result.result: {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"} (type: <class 'dict'>)
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Handle 'output_data' not found in dict keys: ['email_1']
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - No handle matches found for 'output_data', treating result as single-value
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - ✅ Handle mapping success: output_data → input_variables via path 'result': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:29:36 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-07-16 15:29:36 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Filtering out field 'input' with null/empty value: 
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Filtering out field 'input_variables' with null/empty value: None
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Filtering out field 'description' with null/empty value: 
2025-07-16 15:29:36 - WorkflowUtils - DEBUG - Filtering out field 'termination_condition' with null/empty value: 
2025-07-16 15:29:36 - WorkflowUtils - INFO - 🧹 Parameter filtering: 8 → 6 fields (2 null/empty fields removed)
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📌 Using top-level system_message: Analyze multiple emails and return the best one based on scoring criteria
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_type = component
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📌 Added static parameter: execution_type = response
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📌 Added static parameter: agent_config = {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}
2025-07-16 15:29:36 - TransitionHandler - DEBUG - 📌 Added static parameter: variables = {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}
2025-07-16 15:29:36 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:29:36 - TransitionHandler - DEBUG - tool Parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:29:36 - TransitionHandler - INFO - Invoking tool 'AgenticAI' (tool_id: 1) for node 'AgenticAI' in transition 'transition-AgenticAI-*************' with parameters: {'input_variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'agent_type': 'component', 'execution_type': 'response', 'agent_config': {'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'autogen_agent_type': 'Assistant'}, 'variables': {'{email_1': '${{email_1}', '{email_2': '${{email_2}', '{research_data': '${{research_data}', '{company_context': '${{company_context}'}}
2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'transition_id': 'transition-AgenticAI-*************', 'node_label': 'AI Agent Executor', 'tool_name': 'AgenticAI', 'message': 'Connecting to server', 'result': 'Connecting to server AI Agent Executor', 'status': 'connecting', 'sequence': 5, 'workflow_status': 'running'}
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Component agent: query='None', input='None', final_query='None'
2025-07-16 15:29:36 - AgentExecutor - INFO - Executing agent 'AgenticAI' type 'component' execution 'response' via Kafka (request_id: d46b8e45-0368-464a-822a-3d1f6f083ae0) with correlation_id: b107939f-b9ef-4712-9c16-61e78fb9df16, user_id: c1454e90-09ac-40f2-bde2-833387d7b645 using provided producer.
2025-07-16 15:29:36 - AgentExecutor - INFO - Building component agent request for execution_type: response
agent config in build component agent request:  {'id': 'f490c626-1c6e-4203-8566-9b87f586c069', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}
system_message in build component agent request:  Analyze multiple emails and return the best one based on scoring criteria
input_variables in build component agent request:  {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Added correlation_id b107939f-b9ef-4712-9c16-61e78fb9df16 to payload
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Sending request to topic 'agent_message_requests': {'request_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'correlation_id': 'b107939f-b9ef-4712-9c16-61e78fb9df16', 'agent_type': 'component', 'execution_type': 'response', 'query': None, 'variables': {'email_1': "I'll analyze Garry Tan's investment activities and create a personalized email from on how you see the AI agent space evolving, especially given your Praxis AI and Ember Copilot bets. Are you seeing patterns in what separates the winners from the noise?\n\nNo pitch here - just genuinely curious about your perspective as someone who's seen 6,000+ applications and backed companies now worth $226B.\n\nBest,\nJesse\n\nP.S. Saw the news about Tom Blomfield joining as Group Partner. Smart move - his Monzo experience will be invaluable for the fintech cohort.\n\n---\n\nThis email:\n- References specific recent investments (Yuma AI, Praxis AI)\n- Connects to his investment thesis naturally\n- Shares relevant metrics without being pitchy\n- Uses personal, conversational tone\n- Asks for genuine insights rather than funding\n- Shows awareness of recent YC news\n- Avoids mentioning Initialized Capital's restructuring"}, 'agent_config': {'id': 'f490c626-1c6e-4203-8566-9b87f586c069', 'name': 'AI Agent', 'description': 'Basic AI Agent', 'system_message': 'Analyze multiple emails and return the best one based on scoring criteria', 'model_config': {'model_provider': 'anthropic', 'model': 'claude-opus-4-20250514', 'temperature': 0.7, 'max_tokens': 1000}, 'mcps': []}}
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Request d46b8e45-0368-464a-822a-3d1f6f083ae0 sent successfully using provided producer.
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Waiting for single response result for request d46b8e45-0368-464a-822a-3d1f6f083ae0...
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Result consumer received message: Offset=26849
2025-07-16 15:29:36 - AgentExecutor - DEBUG - Processing result payload: {'run_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'session_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'event_type': 'error', 'agent_response': {}, 'success': False, 'message': 'Request validation failed', 'final': True, 'stream_chunk_id': None, 'timestamp': None, 'error': None, 'agent_type': None, 'request_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'error_code': 'ERR_1001', 'details': {'original_error': '3 validation errors for AgentMessageRequest\nquery.str\n  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type\nquery.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/dict_type\nquery.list[any]\n  Input should be a valid list [type=list_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type', 'request_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'timestamp': '2025-07-16T09:59:36.512860Z'}}
2025-07-16 15:29:36 - AgentExecutor - ERROR - Request failed. Error: Request validation failed, Details: {'original_error': '3 validation errors for AgentMessageRequest\nquery.str\n  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type\nquery.dict[any,any]\n  Input should be a valid dictionary [type=dict_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/dict_type\nquery.list[any]\n  Input should be a valid list [type=list_type, input_value=None, input_type=NoneType]\n    For further information visit https://errors.pydantic.dev/2.11/v/list_type', 'request_id': 'd46b8e45-0368-464a-822a-3d1f6f083ae0', 'user_id': 'c1454e90-09ac-40f2-bde2-833387d7b645', 'timestamp': '2025-07-16T09:59:36.512860Z'}
2025-07-16 15:29:36 - AgentExecutor - WARNING - Received error response for request_id d46b8e45-0368-464a-822a-3d1f6f083ae0: Request validation failed
2025-07-16 15:29:36 - AgentExecutor - ERROR - Error during agent execution d46b8e45-0368-464a-822a-3d1f6f083ae0: Agent execution failed: Request validation failed
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 456, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - TransitionHandler - ERROR - Tool execution failed for tool 'AgenticAI' (tool_id: 1) in node 'AgenticAI' of transition 'transition-AgenticAI-*************': Agent execution failed: Request validation failedTraceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 393, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 474, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 456, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Request validation failed

2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id b107939f-b9ef-4712-9c16-61e78fb9df16):
2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'transition_id': 'transition-AgenticAI-*************', 'node_label': 'AI Agent Executor', 'tool_name': 'AgenticAI', 'message': 'Transition faced an error during execution.', 'result': '[ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed', 'status': 'failed', 'sequence': 6, 'workflow_status': 'running'}
2025-07-16 15:29:36 - TransitionHandler - ERROR - Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed')]
2025-07-16 15:29:36 - EnhancedWorkflowEngine - DEBUG - 🔄 Orchestration engine received result for transition-AgenticAI-*************: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - EnhancedWorkflowEngine - DEBUG - 🔄 Result type: <class 'Exception'>, is_list: False
2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-AgenticAI-*************: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-AgenticAI-*************: NoneType: None

2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 393, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 474, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 456, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 118, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 927, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 304, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 148, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed

2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 393, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 474, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 456, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 118, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 927, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 346, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 304, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 148, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed

2025-07-16 15:29:36 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 393, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<4 lines>...
    )
    ^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 474, in execute_tool
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/services/agent_executor.py", line 456, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.agent_executor.AgentExecutionError: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 118, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 927, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/execution/executor_server_kafka.py", line 342, in handle_workflow_result
    execution_success = await execution_task
                        ^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 402, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 346, in execute
    raise e
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/executor_core.py", line 304, in execute
    raise result
  File "/Users/<USER>/Desktop/ruh_ai/backend/orchestration-engine/app/core_/transition_handler.py", line 148, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0' final status: failed, result: Exception in workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0': Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed
2025-07-16 15:29:36 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: b107939f-b9ef-4712-9c16-61e78fb9df16, response: {'status': 'failed', 'result': "Exception in workflow 'b33ceffe-e932-4e14-bdd1-fb9aeebccbb0': Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed", 'workflow_status': 'failed', 'error': 'Exception in transition transition-AgenticAI-*************: Tool execution error: [ERROR] Tool Execution Failed with error: Agent execution failed: Request validation failed', 'error_type': 'Exception'}
2025-07-16 15:29:36 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: b107939f-b9ef-4712-9c16-61e78fb9df16 
