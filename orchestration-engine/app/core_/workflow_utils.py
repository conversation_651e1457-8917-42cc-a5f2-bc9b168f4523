import json
import re
from typing import Any, Dict, List, Optional, Union
from collections import defaultdict
from jsonschema import Draft7Validator
import warnings
from app.utils.enhanced_logger import get_logger
from app.utils.helper_functions import load_schema, fix_invalid_escapes
from app.utils.agentic_operations import resolve_switch_case, format_schema
from app.config.config import settings
from ..core_.state_manager import WorkflowStateManager

logger = get_logger("WorkflowUtils")


class WorkflowUtils:

    def __init__(self, workflow_id=None):
        self.logger = logger
        self.logger.info("WorkflowUtils initialized")
        self.state_manager = WorkflowStateManager(workflow_id)
        self.ENHANCED_SCHEMA = load_schema(settings.schema_file_path)

    def _validate_schema(self, workflow_json: dict):
        """
        Validate the input workflow against the Enhanced Workflow Schema.
        Raises jsonschema.exceptions.ValidationError on failure.
        """
        validator = Draft7Validator(self.ENHANCED_SCHEMA)
        validator.validate(workflow_json)
        self.logger.info("Workflow JSON is valid against the enhanced schema.")

    async def _format_tool_parameters(
        self,
        node_tool_info: dict,  # Kept for compatibility with existing calls
        input_data_configs: dict,
        transition_id: str,
        current_tool_params: dict,
        iteration_context: dict = None,
    ) -> dict:
        """
        Format tool parameters using handle-based resolution.

        Uses direct handle-to-handle mappings from the transition schema to resolve
        parameters without placeholder interpretation. Falls back to AI-based formatting
        only when handle mappings are not available.

        Always returns a dictionary with direct key-value pairs.
        """
        self.logger.debug(f"Formatting tool parameters for transition: {transition_id}")

        # Collect all results from specified transitions
        all_previous_results = {}
        current_previous_results = {}  # For format_schema compatibility

        if input_data_configs:
            for input_data_config in input_data_configs:
                from_transition_id = input_data_config.get("from_transition_id")
                if from_transition_id:
                    result_from_dependency = self.state_manager.get_transition_result(
                        from_transition_id
                    )
                    if result_from_dependency:
                        all_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        current_previous_results[from_transition_id] = (
                            result_from_dependency
                        )
                        self.logger.debug(
                            f"Found results from transition {from_transition_id}: {result_from_dependency}"
                        )

        # Add iteration context under the loop transition ID if available
        if iteration_context:
            loop_transition_id = iteration_context.get("transition_id")
            if loop_transition_id:
                self.logger.debug(f"🔄 Adding iteration context for loop transition {loop_transition_id}: {iteration_context}")
                # Store iteration context under the loop transition ID so handle mappings can find it
                all_previous_results[loop_transition_id] = {
                    "current_item": iteration_context.get("iteration_item"),
                    "iteration_index": iteration_context.get("iteration_index"),
                    "loop_id": iteration_context.get("loop_id")
                }
                current_previous_results[loop_transition_id] = all_previous_results[loop_transition_id]
            else:
                self.logger.warning("🔄 Iteration context missing transition_id, cannot store for handle mapping")

        # If we have no previous results, convert the current params to dictionary format
        if not all_previous_results:
            self.logger.debug(
                "No previous results found, converting current params to dictionary format"
            )
            return self._convert_params_to_dict(current_tool_params)

        # Try handle-based resolution first
        try:
            handle_mappings = self._extract_handle_mappings(input_data_configs)
            if handle_mappings:
                # Count total mappings across all source transitions
                total_mappings = sum(
                    len(mappings) for mappings in handle_mappings.values()
                )
                self.logger.debug(
                    f"Using handle-based resolution with {total_mappings} total mappings from {len(handle_mappings)} source transitions"
                )
                processed_params = self._resolve_handle_data(
                    current_tool_params, all_previous_results, handle_mappings, iteration_context
                )
                return processed_params
            else:
                self.logger.debug(
                    "No handle mappings found, falling back to placeholder resolution"
                )

        except Exception as e:
            self.logger.warning(
                f"Handle-based resolution failed: {str(e)}, falling back to placeholder resolution"
            )

        # Fallback to placeholder-based resolution for backward compatibility
        flattened_results = {}
        for transition_results in all_previous_results.values():
            if isinstance(transition_results, dict):
                flattened_results.update(transition_results)

        try:
            processed_params, has_unresolved_placeholders = (
                self._process_params_for_placeholders(
                    current_tool_params, flattened_results
                )
            )

            # If we have unresolved placeholders, fall back to format_schema
            if has_unresolved_placeholders:
                self.logger.info(
                    f"Direct placeholder replacement couldn't resolve all placeholders for transition {transition_id}. Falling back to AI-based formatting."
                )
                processed_params = await self._fallback_to_format_schema(
                    node_tool_info, current_previous_results, current_tool_params
                )
                return self._convert_params_to_dict(processed_params)

            return processed_params

        except Exception as e:
            self.logger.warning(
                f"Error during parameter resolution for transition {transition_id}: {str(e)}. Falling back to AI-based formatting."
            )
            processed_params = await self._fallback_to_format_schema(
                node_tool_info, current_previous_results, current_tool_params
            )
            return self._convert_params_to_dict(processed_params)

    def _filter_null_empty_values(self, params):
        """
        Filter out null, None, empty string, and undefined values from parameters.

        This method removes fields that have no meaningful value to reduce payload size
        and improve tool execution efficiency.

        Args:
            params: Dictionary of parameters to filter

        Returns:
            dict: Filtered parameters with only meaningful values
        """
        if not isinstance(params, dict):
            return params

        filtered_params = {}

        for key, value in params.items():
            # Skip null, None, empty string values
            if value is None or value == "" or value == "null":
                self.logger.debug(
                    f"Filtering out field '{key}' with null/empty value: {value}"
                )
                continue

            # Skip empty dictionaries and lists
            if isinstance(value, (dict, list)) and len(value) == 0:
                self.logger.debug(
                    f"Filtering out field '{key}' with empty collection: {value}"
                )
                continue

            # Recursively filter nested dictionaries
            if isinstance(value, dict):
                filtered_value = self._filter_null_empty_values(value)
                # Only include if the filtered dict is not empty
                if filtered_value:
                    filtered_params[key] = filtered_value
                else:
                    self.logger.debug(
                        f"Filtering out field '{key}' with empty nested dict after filtering"
                    )
            else:
                # Include all other meaningful values
                filtered_params[key] = value

        return filtered_params

    def _convert_params_to_dict(self, params):
        """
        Convert parameters to dictionary format regardless of input format.

        This method now includes automatic null/empty value filtering to ensure
        only meaningful parameters are sent for tool execution.

        Args:
            params: Parameters in any format (list, dict, etc.)

        Returns:
            dict: Parameters in dictionary format with null/empty values filtered out
        """
        original_count = 0

        if isinstance(params, list):
            result_dict = {}
            for param_item in params:
                if isinstance(param_item, dict):
                    field_name = param_item.get("field_name")
                    field_value = param_item.get("field_value")
                    if field_name:
                        result_dict[field_name] = field_value
                        original_count += 1

            # Apply null/empty value filtering
            filtered_result = self._filter_null_empty_values(result_dict)
            filtered_count = len(filtered_result)

            if original_count != filtered_count:
                self.logger.info(
                    f"🧹 Parameter filtering: {original_count} → {filtered_count} fields "
                    f"({original_count - filtered_count} null/empty fields removed)"
                )

            return filtered_result

        elif isinstance(params, dict):
            original_count = len(params)

            # Apply null/empty value filtering
            filtered_result = self._filter_null_empty_values(params)
            filtered_count = len(filtered_result)

            if original_count != filtered_count:
                self.logger.info(
                    f"🧹 Parameter filtering: {original_count} → {filtered_count} fields "
                    f"({original_count - filtered_count} null/empty fields removed)"
                )

            return filtered_result
        else:
            self.logger.debug("Converting non-dict/non-list params to empty dict")
            return {}

    def _extract_handle_mappings(self, input_data_configs):
        """
        Extract handle mappings from input_data configurations.

        Args:
            input_data_configs: List of input data configurations

        Returns:
            dict: Handle mappings organized by source transition
        """
        handle_mappings = {}

        if not input_data_configs:
            return handle_mappings

        for input_data_config in input_data_configs:
            from_transition_id = input_data_config.get("from_transition_id")
            mappings = input_data_config.get("handle_mappings", [])

            if from_transition_id and mappings:
                # Accumulate mappings instead of overwriting when same source transition
                if from_transition_id not in handle_mappings:
                    handle_mappings[from_transition_id] = []
                handle_mappings[from_transition_id].extend(mappings)
                self.logger.debug(
                    f"Added {len(mappings)} handle mappings from transition {from_transition_id} "
                    f"(total: {len(handle_mappings[from_transition_id])})"
                )

        return handle_mappings

    def _is_loop_iteration_mapping(self, mappings):
        """
        Check if any of the mappings are for loop iteration data (current_item, iteration_index, etc.).

        Args:
            mappings: List of handle mappings

        Returns:
            bool: True if any mapping is for loop iteration data
        """
        loop_iteration_handles = {'current_item', 'iteration_index', 'iteration_data', 'loop_item'}

        for mapping in mappings:
            source_handle_id = mapping.get("source_handle_id", "").lower()
            if source_handle_id in loop_iteration_handles:
                return True
        return False

    def _get_loop_iteration_data(self, source_transition_id):
        """
        Retrieve loop iteration data from state manager for loop transitions.
        Enhanced to check multiple storage locations to resolve race conditions.

        Args:
            source_transition_id: The transition ID to check for loop iteration data

        Returns:
            dict: Loop iteration data if found, None otherwise
        """
        try:
            # Check if this is a loop transition by looking for "LoopNode" in the ID
            if "LoopNode" in source_transition_id:
                # Try to get the current iteration data from state manager
                if hasattr(self, 'state_manager') and self.state_manager:

                    # Method 1: Check transition_results for temporary iteration data
                    iteration_data = self.state_manager.transition_results.get(source_transition_id)
                    if iteration_data and isinstance(iteration_data, dict) and 'current_item' in iteration_data:
                        self.logger.debug(f"🔄 Retrieved loop iteration data from memory: {iteration_data}")
                        return iteration_data

                    # Method 2: Check for backup keys with current iteration data
                    # Look for backup keys that might contain iteration data
                    for key in self.state_manager.transition_results.keys():
                        if key.startswith(f"backup_{source_transition_id}_iteration_"):
                            backup_data = self.state_manager.transition_results.get(key)
                            if backup_data and isinstance(backup_data, dict) and 'current_item' in backup_data:
                                self.logger.debug(f"🔄 Retrieved loop iteration data from backup key {key}: {backup_data}")
                                return backup_data

                    # Method 3: Check Redis for loop iteration keys
                    try:
                        # Look for loop iteration keys in Redis
                        if hasattr(self.state_manager, 'results_redis_manager') and self.state_manager.results_redis_manager:
                            # Try to find iteration-specific keys
                            for i in range(10):  # Check last 10 iterations
                                iteration_key = f"loop_iteration_{source_transition_id}_{i}"
                                iteration_result = self.state_manager.get_transition_result(iteration_key)
                                if iteration_result and isinstance(iteration_result, dict) and 'current_item' in iteration_result:
                                    self.logger.debug(f"🔄 Retrieved loop iteration data from Redis key {iteration_key}: {iteration_result}")
                                    return iteration_result
                    except Exception as redis_e:
                        self.logger.debug(f"Redis lookup failed for loop iteration data: {str(redis_e)}")

                    # Method 4: Check persistent storage as fallback
                    stored_result = self.state_manager.get_transition_result(source_transition_id)
                    if stored_result and isinstance(stored_result, dict) and 'current_item' in stored_result:
                        self.logger.debug(f"🔄 Retrieved loop iteration data from persistent storage: {stored_result}")
                        return stored_result

        except Exception as e:
            self.logger.debug(f"Could not retrieve loop iteration data for {source_transition_id}: {str(e)}")

        return None

    def _get_source_results_with_iteration_context(
        self, all_previous_results, source_transition_id, iteration_context=None
    ):
        """
        Get source results, checking for iteration-specific IDs if in loop context.

        Args:
            all_previous_results: Results from all previous transitions
            source_transition_id: Base transition ID to look for
            iteration_context: Optional iteration context for loop execution

        Returns:
            dict: Source results from either base or iteration-specific transition ID
        """
        self.logger.debug(
            f"🔍 PATH TRACKING: Looking for results for transition {source_transition_id}, "
            f"iteration_context: {iteration_context is not None}"
        )

        # If we have iteration context, try iteration-specific ID FIRST
        if iteration_context:
            iteration_index = iteration_context.get("iteration_index")
            if iteration_index is not None:
                iteration_transition_id = f"{source_transition_id}_iteration_{iteration_index}"
                source_results = all_previous_results.get(iteration_transition_id, {})
                if source_results:
                    self.logger.debug(
                        f"🔄 ITERATION FIX: Found results for iteration-specific ID: {iteration_transition_id}"
                    )
                    return source_results
                else:
                    self.logger.debug(
                        f"🔄 ITERATION FIX: No results found for iteration-specific ID: {iteration_transition_id}"
                    )

        # Fallback to base transition ID
        source_results = all_previous_results.get(source_transition_id, {})
        self.logger.debug(
            f"🔍 PATH TRACKING: Base ID {source_transition_id} results: "
            f"{'found' if source_results else 'not found'}"
        )

        # If still no results and we have iteration context, log available keys for debugging
        if not source_results and iteration_context:
            available_keys = [k for k in all_previous_results.keys() if source_transition_id in k]
            self.logger.debug(f"🔄 ITERATION FIX: Available keys containing '{source_transition_id}': {available_keys}")

        return source_results

    def _resolve_handle_data(
        self, current_tool_params, all_previous_results, handle_mappings, iteration_context=None
    ):
        """
        Resolve tool parameters using direct handle-to-handle mappings.

        This method now includes automatic null/empty value filtering to ensure
        only meaningful parameters are included in the final resolved parameters.
        Enhanced with loop iteration data resolution to handle current_item mappings.

        Args:
            current_tool_params: Current tool parameters
            all_previous_results: Results from all previous transitions
            handle_mappings: Handle mappings from input data configs
            iteration_context: Optional iteration context for loop execution

        Returns:
            dict: Resolved parameters with null/empty values filtered out
        """
        resolved_params = {}

        # Convert current params to dict format for processing
        current_params_dict = self._convert_params_to_dict(current_tool_params)

        # Count total mappings and track success/failure
        total_mappings = sum(len(mappings) for mappings in handle_mappings.values())
        successful_mappings = 0
        failed_mappings = 0

        # Process each handle mapping
        for source_transition_id, mappings in handle_mappings.items():
            # Try to get source results, checking for iteration-specific IDs if in loop context
            source_results = self._get_source_results_with_iteration_context(
                all_previous_results, source_transition_id, iteration_context
            )

            # ENHANCED: Check for loop iteration data if source_results is empty or doesn't contain expected handles
            needs_loop_data = self._is_loop_iteration_mapping(mappings)
            has_loop_data = source_results and any(key in source_results for key in ['current_item', 'iteration_index'])

            self.logger.debug(f"🔍 Processing handle mappings for {source_transition_id}: needs_loop_data={needs_loop_data}, has_loop_data={has_loop_data}, source_results_keys={list(source_results.keys()) if source_results else []}")
            self.logger.debug(f"🔍 Available transition IDs in all_previous_results: {list(all_previous_results.keys())}")

            if needs_loop_data and not has_loop_data:
                # Try to get loop iteration data from state manager
                loop_iteration_data = self._get_loop_iteration_data(source_transition_id)
                if loop_iteration_data:
                    self.logger.debug(f"🔄 Found loop iteration data for {source_transition_id}: {loop_iteration_data}")
                    source_results = loop_iteration_data
                elif not source_results:
                    self.logger.warning(
                        f"No results found for source transition: {source_transition_id}"
                    )
                    failed_mappings += len(mappings)
                    continue

            for mapping in mappings:
                source_handle_id = mapping.get("source_handle_id")
                target_handle_id = mapping.get("target_handle_id")
                edge_id = mapping.get("edge_id", "")

                if not source_handle_id or not target_handle_id:
                    self.logger.warning(f"Invalid handle mapping: {mapping}")
                    failed_mappings += 1
                    continue

                # Extract data using universal result path resolution
                extracted_value = self._extract_data_by_handle(
                    source_results, source_handle_id, source_transition_id
                )

                if extracted_value is not None:
                    resolved_params[target_handle_id] = extracted_value
                    successful_mappings += 1
                    self.logger.debug(
                        f"✅ Mapped {source_handle_id} → {target_handle_id}: {extracted_value} (edge: {edge_id})"
                    )
                else:
                    failed_mappings += 1
                    self.logger.warning(
                        f"❌ Could not extract data for handle {source_handle_id} from transition {source_transition_id}"
                    )

        # Merge with any static parameters that don't have handle mappings
        static_params_added = 0
        for param_name, param_value in current_params_dict.items():
            if param_name not in resolved_params:
                # Only include if it's not a placeholder (no ${...} pattern)
                if not (
                    isinstance(param_value, str)
                    and "${" in param_value
                    and "}" in param_value
                ):
                    # Check if the static parameter has a meaningful value before adding
                    if not (
                        param_value is None
                        or param_value == ""
                        or param_value == "null"
                    ):
                        resolved_params[param_name] = param_value
                        static_params_added += 1
                        self.logger.debug(
                            f"Added static parameter: {param_name} = {param_value}"
                        )
                    else:
                        self.logger.debug(
                            f"Skipped null/empty static parameter: {param_name} = {param_value}"
                        )

        if static_params_added > 0:
            self.logger.debug(
                f"Added {static_params_added} static parameters to resolved params"
            )

        # Log comprehensive mapping results
        self.logger.info(
            f"🎯 Parameter mapping complete: {successful_mappings}/{total_mappings} successful"
        )

        if failed_mappings > 0:
            self.logger.warning(
                f"⚠️ {failed_mappings} handle mappings failed - this may cause tool execution errors"
            )

        self.logger.debug(
            f"Resolved parameters using handle mappings: {resolved_params}"
        )

        # Apply null/empty value filtering to resolved parameters
        original_param_count = len(resolved_params)
        filtered_params = self._filter_null_empty_values(resolved_params)
        filtered_param_count = len(filtered_params)

        if original_param_count != filtered_param_count:
            self.logger.info(
                f"🧹 Handle data filtering: {original_param_count} → {filtered_param_count} parameters "
                f"({original_param_count - filtered_param_count} null/empty parameters removed)"
            )
        else:
            self.logger.debug("No null/empty parameters found in resolved handle data")

        self.logger.debug(f"Final filtered resolved parameters: {filtered_params}")
        return filtered_params

    def _extract_data_by_handle(self, source_results, handle_id, source_transition_id):
        """
        Extract data from source results using universal result path resolution.

        This method implements dynamic discovery of result structures without
        hardcoded patterns, supporting any node type and result format.

        Special handling for conditional components:
        - Conditional components return routing_decision metadata for orchestration
        - But for data flow, we need to pass the original input data, not the routing metadata
        - This method detects conditional components and extracts the original input data

        Logic:
        1. Check if this is a conditional component result
        2. For conditional components, extract original input data instead of routing decision
        3. For other components, use standard result extraction logic
        4. Always check result.result as the final result location
        5. If it's a complex object (dict/list), try to find handle matches
        6. If no handle matches found, treat as single-value result and return as-is

        Args:
            source_results: Results from the source transition
            handle_id: Handle ID to extract data for
            source_transition_id: Source transition ID for logging

        Returns:
            Any: Extracted data or None if not found
        """
        if not isinstance(source_results, dict):
            self.logger.warning(f"Source results is not a dict: {type(source_results)}")
            return None

        # DEBUG: Log the actual structure being processed
        self.logger.debug(
            f"🔍 Processing source_results for handle '{handle_id}': {source_results}"
        )
        self.logger.debug(
            f"🔍 Available keys in source_results: {list(source_results.keys())}"
        )

        # SPECIAL HANDLING: Check if this is a conditional component result
        if self._is_conditional_component_result(source_results):
            self.logger.info(
                f"🔀 Detected conditional component result for handle '{handle_id}'"
            )
            return self._extract_conditional_component_data(
                source_results, handle_id, source_transition_id
            )

        # STANDARD HANDLING: For non-conditional components
        # Step 1: Extract result.result as the final result location
        final_result = self._extract_by_path(source_results, "result.result")
        if final_result is None:
            # Fallback: Try just "result" in case it's single-nested
            final_result = self._extract_by_path(source_results, "result")
            if final_result is None:
                # Additional fallback: Check if handle exists directly in source_results
                # This handles cases like loop nodes that output handles directly
                if handle_id in source_results:
                    self.logger.debug(f"Found handle '{handle_id}' directly in source_results")
                    return source_results[handle_id]

                self.logger.warning(
                    f"Could not find result.result or result in source_results for handle {handle_id}"
                )
                self.logger.debug(f"Path tracking - Source results structure")
                return None
            else:
                self.logger.debug(f"Found single-nested result for handle {handle_id}")
        else:
            self.logger.debug(
                f"Found double-nested result.result for handle {handle_id}"
            )

        self.logger.debug(f"Found result.result:(type: {type(final_result)})")

        # Step 2: Check if this is a complex object with handle mappings
        extracted_value = self._extract_handle_from_result(final_result, handle_id)

        if extracted_value is not None:
            self.logger.debug(f"Successfully extracted handle '{handle_id}'")
            return extracted_value

        # Step 3: No handle matches found - treat as single-value result
        self.logger.debug(
            f"No handle matches found for '{handle_id}', treating result.result as single-value"
        )
        return final_result

    def _is_conditional_component_result(self, source_results):
        """
        Detect if the source results come from a conditional component.

        Conditional components have a specific result structure:
        - Contains 'routing_decision' key
        - Contains 'metadata' key
        - The routing_decision contains routing information, not actual data

        Args:
            source_results: Results from the source transition

        Returns:
            bool: True if this is a conditional component result
        """
        if not isinstance(source_results, dict):
            return False

        # Check for conditional component signature
        result = source_results.get("result", {})
        if isinstance(result, dict):
            has_routing_decision = "routing_decision" in result
            has_metadata = "metadata" in result

            # Additional check: routing_decision should contain transition info
            if has_routing_decision:
                routing_decision = result.get("routing_decision", {})
                has_transition_info = (
                    "target_transition" in routing_decision
                    or "target_transitions" in routing_decision
                )

                if has_routing_decision and has_metadata and has_transition_info:
                    self.logger.debug(
                        "✅ Detected conditional component result structure"
                    )
                    return True

        return False

    def _extract_conditional_component_data(
        self, source_results, handle_id, source_transition_id
    ):
        """
        Extract data from conditional component results.

        For conditional components, we need to extract the original input data that was
        passed to the conditional component, not the routing decision metadata.

        The conditional component receives input data and returns routing decisions,
        but for data flow purposes, we want to pass through the original input data.

        Args:
            source_results: Results from the conditional component
            handle_id: Handle ID to extract data for (e.g., "condition_1", "condition_2", "default")
            source_transition_id: Source transition ID for logging

        Returns:
            Any: The original input data that should flow through the conditional node
        """
        self.logger.info(
            f"🔀 Extracting conditional component data for handle '{handle_id}'"
        )

        # For conditional components, we need to find the original input data
        # The conditional component processes input and returns routing decisions,
        # but the actual data should flow through unchanged

        # Try to find the original input data in the conditional component's input
        # This might be stored in different locations depending on the implementation

        # Method 1: Check if there's input data stored alongside the routing decision
        result = source_results.get("result", {})
        if isinstance(result, dict):
            # Look for input data fields (prioritize input_data which we just added)
            input_data_fields = ["input_data", "node_output", "original_input", "data"]
            for field in input_data_fields:
                if field in result:
                    input_data = result[field]
                    self.logger.info(
                        f"🔀 Found conditional input data in '{field}': {input_data}"
                    )
                    return input_data

        # Method 2: Check the transition's input parameters
        # We need to get the original input that was passed to the conditional component
        # This requires looking at the conditional component's input parameters

        # For now, let's try to extract from the state manager if available
        if hasattr(self, "state_manager") and self.state_manager:
            # Try to get the transition details to find the original input
            transition_details = self.state_manager.get_transition_details(
                source_transition_id
            )
            if transition_details:
                input_params = transition_details.get("input_parameters", {})

                # Look for the node_output parameter that was passed to the conditional component
                if "node_output" in input_params:
                    original_input = input_params["node_output"]
                    self.logger.info(
                        f"🔀 Found original input from transition details: {original_input}"
                    )
                    return original_input

        # Method 3: Fallback - try to find any non-metadata data in the result
        if isinstance(result, dict):
            # Exclude known metadata fields
            metadata_fields = {
                "routing_decision",
                "metadata",
                "status",
                "execution_time_ms",
            }
            for key, value in result.items():
                if key not in metadata_fields:
                    self.logger.info(
                        f"🔀 Using fallback data from field '{key}': {value}"
                    )
                    return value

        # Method 4: Last resort - return a placeholder or None
        self.logger.warning(
            f"🔀 Could not extract original input data for conditional component handle '{handle_id}'. "
            f"This may cause downstream components to receive incorrect data."
        )

        # Return None to indicate no data found - this will be handled by the calling code
        return None

    def _extract_handle_from_result(self, final_result, handle_id):
        """
        Extract handle from the final result using comprehensive search.

        This method loops through all possible locations to find the handle:
        1. If final_result is a dict, check if handle_id is a direct key
        2. If final_result is a list, loop through all elements to find handle
        3. Return None if handle not found anywhere

        Args:
            final_result: The result.result data (can be dict, list, or primitive)
            handle_id: The handle ID to search for

        Returns:
            Any: The extracted handle value or None if not found
        """
        if final_result is None:
            return None

        # Case 1: final_result is a dictionary - check direct key access
        if isinstance(final_result, dict):
            if handle_id in final_result:
                self.logger.debug(f"Found handle '{handle_id}' directly in dict")
                return final_result[handle_id]
            else:
                self.logger.debug(
                    f"Handle '{handle_id}' not found in dict keys: {list(final_result.keys())}"
                )
                return None

        # Case 2: final_result is a list - loop through all elements
        elif isinstance(final_result, list):
            self.logger.debug(
                f"Searching for handle '{handle_id}' in list with {len(final_result)} elements"
            )

            for i, element in enumerate(final_result):
                if isinstance(element, dict) and handle_id in element:
                    self.logger.debug(f"Found handle '{handle_id}' in list element {i}")
                    return element[handle_id]

            self.logger.debug(f"Handle '{handle_id}' not found in any list elements")
            return None

        # Case 3: final_result is a primitive value - no handle extraction possible
        else:
            self.logger.debug(
                f"final_result is primitive type {type(final_result)}, no handle extraction possible"
            )
            return None

    def _get_nested_structure_summary(self, data, max_depth=3, current_depth=0):
        """
        Get a summary of nested data structure for debugging.

        Args:
            data: Data to analyze
            max_depth: Maximum depth to analyze
            current_depth: Current recursion depth

        Returns:
            str: Structure summary
        """
        if current_depth >= max_depth:
            return f"{type(data).__name__}(...)"

        if isinstance(data, dict):
            if not data:
                return "{}"
            keys = list(data.keys())[:3]  # Show first 3 keys
            key_summaries = []
            for key in keys:
                value_summary = self._get_nested_structure_summary(
                    data[key], max_depth, current_depth + 1
                )
                key_summaries.append(f"'{key}': {value_summary}")

            result = "{" + ", ".join(key_summaries)
            if len(data) > 3:
                result += f", ...{len(data)-3} more"
            result += "}"
            return result
        elif isinstance(data, list):
            if not data:
                return "[]"
            return f"[{len(data)} items]"
        else:
            return f"{type(data).__name__}({str(data)[:50]}{'...' if len(str(data)) > 50 else ''})"

    def _extract_by_path(self, data, path):
        """
        Extract data from nested dictionary using dot notation path.
        Now supports list handling - if we encounter a list, we try to extract from the first element.

        Args:
            data: Source data dictionary
            path: Dot notation path (e.g., "result.field_name")

        Returns:
            Any: Extracted data or None if path not found
        """
        if not path:
            return None

        current = data
        path_parts = path.split(".")

        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            elif isinstance(current, list) and current:
                # If we encounter a list, try to extract from the first element
                # This handles cases like {"result": {"result": [{"script": "..."}]}}
                first_element = current[0]
                if isinstance(first_element, dict) and part in first_element:
                    current = first_element[part]
                else:
                    return None
            else:
                return None

        # If we end up with a list, return the first element if it's a single-item list
        # This handles cases where the final result is a list with one element
        if isinstance(current, list) and len(current) == 1:
            return current[0]

        return current

    def create_universal_parameter_mapping(self, handle_mappings, all_previous_results, iteration_context=None):
        """
        Create universal parameter mapping system that maps handle IDs directly to parameter values.

        This method provides a complete mapping system that works with any node type
        and result structure without hardcoded logic.

        Args:
            handle_mappings: Handle mappings from input data configurations
            all_previous_results: Results from all previous transitions
            iteration_context: Optional iteration context for loop execution

        Returns:
            dict: Complete parameter mapping with handle validation
        """
        parameter_mapping = {
            "resolved_parameters": {},
            "mapping_metadata": {
                "total_mappings": 0,
                "successful_mappings": 0,
                "failed_mappings": 0,
                "mapping_details": [],
            },
        }

        for source_transition_id, mappings in handle_mappings.items():
            # Try to get source results, checking for iteration-specific IDs if in loop context
            source_results = self._get_source_results_with_iteration_context(
                all_previous_results, source_transition_id, iteration_context
            )

            for mapping in mappings:
                source_handle_id = mapping.get("source_handle_id")
                target_handle_id = mapping.get("target_handle_id")
                edge_id = mapping.get("edge_id", "")

                parameter_mapping["mapping_metadata"]["total_mappings"] += 1

                mapping_detail = {
                    "source_transition_id": source_transition_id,
                    "source_handle_id": source_handle_id,
                    "target_handle_id": target_handle_id,
                    "edge_id": edge_id,
                    "status": "pending",
                    "extracted_value": None,
                    "extraction_path": None,
                    "error": None,
                }

                try:
                    # Use enhanced data extraction with path tracking
                    extracted_value, extraction_path = (
                        self._extract_data_with_path_tracking(
                            source_results, source_handle_id, source_transition_id
                        )
                    )

                    if extracted_value is not None:
                        parameter_mapping["resolved_parameters"][
                            target_handle_id
                        ] = extracted_value
                        mapping_detail.update(
                            {
                                "status": "success",
                                "extracted_value": extracted_value,
                                "extraction_path": extraction_path,
                            }
                        )
                        parameter_mapping["mapping_metadata"][
                            "successful_mappings"
                        ] += 1

                        self.logger.debug(
                            f"✅ Handle mapping success: {source_handle_id} → {target_handle_id} "
                            f"via path '{extraction_path}': {extracted_value}"
                        )
                    else:
                        mapping_detail.update(
                            {
                                "status": "failed",
                                "error": f"No data found for handle {source_handle_id}",
                            }
                        )
                        parameter_mapping["mapping_metadata"]["failed_mappings"] += 1

                        self.logger.warning(
                            f"❌ Handle mapping failed: {source_handle_id} → {target_handle_id} "
                            f"(no data found)"
                        )

                except Exception as e:
                    mapping_detail.update({"status": "error", "error": str(e)})
                    parameter_mapping["mapping_metadata"]["failed_mappings"] += 1

                    self.logger.error(
                        f"💥 Handle mapping error: {source_handle_id} → {target_handle_id}: {str(e)}"
                    )

                parameter_mapping["mapping_metadata"]["mapping_details"].append(
                    mapping_detail
                )

        # Log comprehensive mapping summary
        metadata = parameter_mapping["mapping_metadata"]
        self.logger.info(
            f"🎯 Universal parameter mapping complete: {metadata['successful_mappings']}/{metadata['total_mappings']} successful"
        )

        if metadata["failed_mappings"] > 0:
            self.logger.warning(
                f"⚠️ {metadata['failed_mappings']} universal handle mappings failed - this may cause tool execution errors"
            )

        return parameter_mapping

    def _extract_data_with_path_tracking(
        self, source_results, handle_id, source_transition_id
    ):
        """
        Extract data with path tracking for enhanced debugging and validation.
        Uses the same logic as _extract_data_by_handle but tracks the extraction path.

        Args:
            source_results: Results from the source transition
            handle_id: Handle ID to extract data for
            source_transition_id: Source transition ID for logging

        Returns:
            tuple: (extracted_value, extraction_path) or (None, None) if not found
        """
        if not isinstance(source_results, dict):
            self.logger.debug(
                f"Source results is not a dict for handle {handle_id}: {type(source_results)}"
            )
            return None, None

        # DEBUG: Log the actual structure being processed
        self.logger.debug(
            f"🔍 PATH TRACKING: Processing source_results for handle '{handle_id}': {source_results}"
        )
        self.logger.debug(
            f"🔍 PATH TRACKING: Available keys in source_results: {list(source_results.keys())}"
        )

        # SPECIAL HANDLING: Check if this is a conditional component result
        if self._is_conditional_component_result(source_results):
            self.logger.info(
                f"🔀 PATH TRACKING: Detected conditional component result for handle '{handle_id}'"
            )
            extracted_data = self._extract_conditional_component_data(
                source_results, handle_id, source_transition_id
            )
            if extracted_data is not None:
                return extracted_data, "result.input_data"
            # If no data found, continue with standard extraction

        # Step 1: Extract result.result as the final result location
        final_result = self._extract_by_path(source_results, "result.result")
        extraction_path_prefix = "result.result"
        if final_result is None:
            # Fallback: Try just "result" in case it's single-nested
            final_result = self._extract_by_path(source_results, "result")
            extraction_path_prefix = "result"
            if final_result is None:
                # Additional fallback: Check if handle exists directly in source_results
                # This handles cases like loop nodes that output handles directly
                if handle_id in source_results:
                    self.logger.debug(f"Path tracking - Found handle '{handle_id}' directly in source_results")
                    return source_results[handle_id], handle_id

                self.logger.debug(
                    f"Could not find result.result or result in source_results for handle {handle_id}"
                )
                self.logger.debug(f"Path tracking - Source results structure")
                return None, None
            else:
                self.logger.debug(
                    f"Path tracking - Found single-nested result for handle {handle_id}: {final_result}"
                )
        else:
            self.logger.debug(
                f"Path tracking - Found double-nested result.result for handle {handle_id}: {final_result}"
            )

        self.logger.debug(
            f"Found result.result: {final_result} (type: {type(final_result)})"
        )

        # Step 2: Check if this is a complex object with handle mappings
        extracted_value = self._extract_handle_from_result(final_result, handle_id)

        if extracted_value is not None:
            # Determine the extraction path based on the result structure
            if isinstance(final_result, dict):
                extraction_path = f"{extraction_path_prefix}.{handle_id}"
            elif isinstance(final_result, list):
                # Find which list element contained the handle
                for i, element in enumerate(final_result):
                    if isinstance(element, dict) and handle_id in element:
                        extraction_path = f"{extraction_path_prefix}[{i}].{handle_id}"
                        break
                else:
                    extraction_path = (
                        f"{extraction_path_prefix}.{handle_id}"  # fallback
                    )
            else:
                extraction_path = extraction_path_prefix

            self.logger.debug(
                f"Successfully extracted handle '{handle_id}' with path '{extraction_path}': {extracted_value}"
            )
            return extracted_value, extraction_path

        # Step 3: No handle matches found - treat as single-value result
        self.logger.debug(
            f"No handle matches found for '{handle_id}', treating {extraction_path_prefix} as single-value"
        )
        return final_result, extraction_path_prefix

    def _is_single_value_result(self, data):
        """
        Determine if the data represents a single-value result or wrapped/nested data.

        This method uses heuristics to differentiate between:
        1. Single-value results: {'testing': '123', 'make': 'new request'}
        2. Wrapped data: {'result': {...}, 'status': 'completed', 'timestamp': 123}

        Args:
            data: Dictionary to analyze

        Returns:
            bool: True if this appears to be a single-value result, False if wrapped data
        """
        if not isinstance(data, dict) or not data:
            return False

        # Common wrapper/metadata keys that suggest this is NOT a single-value result
        wrapper_keys = {
            "result",
            "data",
            "response",
            "output",
            "content",
            "payload",
            "status",
            "timestamp",
            "transition_id",
            "node_id",
            "tool_name",
            "metadata",
            "info",
            "success",
            "error",
            "message",
            "code",
        }

        # Check if any keys are wrapper/metadata keys
        data_keys = set(data.keys())
        wrapper_keys_found = data_keys.intersection(wrapper_keys)

        # If we find wrapper keys, this is likely wrapped data
        if wrapper_keys_found:
            self.logger.debug(
                f"Found wrapper keys {wrapper_keys_found}, treating as wrapped data"
            )
            return False

        # Check for single-key dictionaries that might be wrappers
        if len(data) == 1:
            single_key = list(data.keys())[0]
            single_value = data[single_key]

            # If the single key is a wrapper-like name and value is complex, it's wrapped
            if single_key.lower() in wrapper_keys and isinstance(
                single_value, (dict, list)
            ):
                self.logger.debug(f"Single key '{single_key}' appears to be a wrapper")
                return False

            # If the single value is a simple type, it's likely a single-value result
            if isinstance(single_value, (str, int, float, bool)):
                self.logger.debug(
                    f"Single key '{single_key}' with simple value, treating as single-value result"
                )
                return True

        # Check the nature of the values
        complex_values = 0
        simple_values = 0

        for key, value in data.items():
            if isinstance(value, (dict, list)):
                complex_values += 1
            else:
                simple_values += 1

        # If most values are simple types, it's likely a single-value result
        if simple_values > complex_values:
            self.logger.debug(
                f"Majority simple values ({simple_values} vs {complex_values}), treating as single-value result"
            )
            return True

        # If we have a mix but no clear wrapper patterns, lean towards single-value
        # This handles cases like {'user_data': {...}, 'preferences': {...}}
        if complex_values > 0 and simple_values > 0:
            self.logger.debug(f"Mixed value types, treating as single-value result")
            return True

        # Default: if all values are complex and no wrapper keys found,
        # it's ambiguous but lean towards single-value result
        self.logger.debug(f"Ambiguous structure, defaulting to single-value result")
        return True

    def create_dynamic_result_structure_analyzer(
        self, result_data, node_type="unknown"
    ):
        """
        Analyze any result structure dynamically to understand its format.

        This method provides comprehensive analysis of result structures from any
        node type without hardcoded assumptions about nesting or field names.

        Args:
            result_data: Result data from any node type
            node_type: Type of node that produced the result (for context)

        Returns:
            dict: Analysis report with structure insights
        """
        analysis = {
            "node_type": node_type,
            "result_type": type(result_data).__name__,
            "structure_pattern": "unknown",
            "available_paths": [],
            "handle_candidates": [],
            "nesting_depth": 0,
            "field_analysis": {},
            "recommendations": [],
        }

        if result_data is None:
            analysis["structure_pattern"] = "null_result"
            analysis["recommendations"].append("Result is null - check node execution")
            return analysis

        # Analyze structure pattern
        if isinstance(result_data, dict):
            analysis["structure_pattern"] = "dictionary"
            analysis.update(self._analyze_dict_structure(result_data, "", 0))
        elif isinstance(result_data, list):
            analysis["structure_pattern"] = "array"
            analysis.update(self._analyze_array_structure(result_data))
        elif isinstance(result_data, str):
            analysis["structure_pattern"] = "string"
            analysis["available_paths"] = ["<direct_string>"]
            analysis["recommendations"].append(
                "String result - consider if this should be parsed as JSON"
            )
        else:
            analysis["structure_pattern"] = "primitive"
            analysis["available_paths"] = ["<direct_value>"]

        # Generate recommendations based on analysis
        self._generate_structure_recommendations(analysis)

        self.logger.debug(
            f"🔍 Result structure analysis for {node_type}: {analysis['structure_pattern']}"
        )
        return analysis

    def _analyze_dict_structure(self, data, path_prefix="", depth=0):
        """
        Analyze dictionary structure recursively.

        Args:
            data: Dictionary to analyze
            path_prefix: Current path prefix
            depth: Current nesting depth

        Returns:
            dict: Analysis results for dictionary structure
        """
        analysis = {
            "available_paths": [],
            "handle_candidates": [],
            "nesting_depth": depth,
            "field_analysis": {},
        }

        max_depth = depth

        for key, value in data.items():
            current_path = f"{path_prefix}.{key}" if path_prefix else key
            analysis["available_paths"].append(current_path)

            # Analyze field type and content
            field_info = {
                "type": type(value).__name__,
                "path": current_path,
                "depth": depth,
                "is_handle_candidate": False,
            }

            # Check if this could be a handle output
            if isinstance(value, (str, int, float, bool)) and value is not None:
                field_info["is_handle_candidate"] = True
                analysis["handle_candidates"].append(current_path)
            elif isinstance(value, dict) and value:
                # Recursively analyze nested dictionaries
                nested_analysis = self._analyze_dict_structure(
                    value, current_path, depth + 1
                )
                analysis["available_paths"].extend(nested_analysis["available_paths"])
                analysis["handle_candidates"].extend(
                    nested_analysis["handle_candidates"]
                )
                analysis["field_analysis"].update(nested_analysis["field_analysis"])
                max_depth = max(max_depth, nested_analysis["nesting_depth"])
            elif isinstance(value, list) and value:
                # Analyze array content
                field_info["array_length"] = len(value)
                if value and isinstance(value[0], dict):
                    # Array of objects - analyze first object as sample
                    nested_analysis = self._analyze_dict_structure(
                        value[0], f"{current_path}[0]", depth + 1
                    )
                    analysis["available_paths"].extend(
                        nested_analysis["available_paths"]
                    )
                    analysis["handle_candidates"].extend(
                        nested_analysis["handle_candidates"]
                    )

            analysis["field_analysis"][current_path] = field_info

        analysis["nesting_depth"] = max_depth
        return analysis

    def _analyze_array_structure(self, data):
        """
        Analyze array structure.

        Args:
            data: Array to analyze

        Returns:
            dict: Analysis results for array structure
        """
        analysis = {
            "available_paths": [],
            "handle_candidates": [],
            "nesting_depth": 1,
            "field_analysis": {},
        }

        if not data:
            analysis["available_paths"] = ["<empty_array>"]
            return analysis

        # Analyze first element as representative
        first_element = data[0]
        analysis["field_analysis"]["array_info"] = {
            "length": len(data),
            "element_type": type(first_element).__name__,
            "is_homogeneous": all(type(item) == type(first_element) for item in data),
        }

        if isinstance(first_element, dict):
            # Array of objects
            nested_analysis = self._analyze_dict_structure(first_element, "[0]", 1)
            analysis["available_paths"].extend(nested_analysis["available_paths"])
            analysis["handle_candidates"].extend(nested_analysis["handle_candidates"])
            analysis["nesting_depth"] = nested_analysis["nesting_depth"]
        else:
            # Array of primitives
            analysis["available_paths"] = ["[0]", "[*]"]  # First element or any element
            analysis["handle_candidates"] = ["[0]"]

        return analysis

    def _generate_structure_recommendations(self, analysis):
        """
        Generate recommendations based on structure analysis.

        Args:
            analysis: Structure analysis to enhance with recommendations
        """
        structure_pattern = analysis["structure_pattern"]
        nesting_depth = analysis["nesting_depth"]
        handle_candidates = analysis["handle_candidates"]

        # Recommendations based on structure pattern
        if structure_pattern == "dictionary":
            if nesting_depth == 0:
                analysis["recommendations"].append(
                    "Flat dictionary - direct field access recommended"
                )
            elif nesting_depth == 1:
                analysis["recommendations"].append(
                    "Single-level nesting - check for 'result' or 'data' fields"
                )
            elif nesting_depth > 2:
                analysis["recommendations"].append(
                    "Deep nesting detected - consider flattening for handle access"
                )

            # Check for common patterns
            available_paths = analysis["available_paths"]
            if "result" in available_paths:
                analysis["recommendations"].append(
                    "'result' field found - likely primary output location"
                )
            if "data" in available_paths:
                analysis["recommendations"].append(
                    "'data' field found - possible API response format"
                )
            if "output_data" in available_paths:
                analysis["recommendations"].append(
                    "'output_data' field found - structured output format"
                )

        elif structure_pattern == "array":
            analysis["recommendations"].append(
                "Array result - consider if first element contains target data"
            )

        elif structure_pattern == "string":
            analysis["recommendations"].append(
                "String result - verify if JSON parsing is needed"
            )

        # Recommendations based on handle candidates
        if not handle_candidates:
            analysis["recommendations"].append(
                "No obvious handle candidates found - check for nested structures"
            )
        elif len(handle_candidates) == 1:
            analysis["recommendations"].append(
                f"Single handle candidate: {handle_candidates[0]}"
            )
        else:
            analysis["recommendations"].append(
                f"Multiple handle candidates found: {len(handle_candidates)} options"
            )

    def validate_handle_mapping_compatibility(
        self, handle_mappings, all_previous_results
    ):
        """
        Validate handle mapping compatibility and provide detailed analysis.

        Args:
            handle_mappings: Handle mappings to validate
            all_previous_results: Available results for validation

        Returns:
            dict: Validation report with compatibility analysis
        """
        validation_report = {
            "overall_compatibility": "unknown",
            "total_mappings": 0,
            "compatible_mappings": 0,
            "incompatible_mappings": 0,
            "missing_sources": 0,
            "validation_details": [],
        }

        for source_transition_id, mappings in handle_mappings.items():
            source_available = source_transition_id in all_previous_results
            source_results = all_previous_results.get(source_transition_id, {})

            for mapping in mappings:
                source_handle_id = mapping.get("source_handle_id")
                target_handle_id = mapping.get("target_handle_id")

                validation_report["total_mappings"] += 1

                validation_detail = {
                    "source_transition_id": source_transition_id,
                    "source_handle_id": source_handle_id,
                    "target_handle_id": target_handle_id,
                    "source_available": source_available,
                    "data_available": False,
                    "compatibility": "incompatible",
                    "issues": [],
                }

                if not source_available:
                    validation_detail["issues"].append(
                        f"Source transition {source_transition_id} not available"
                    )
                    validation_report["missing_sources"] += 1
                else:
                    # Check if data is available for the handle
                    extracted_value, extraction_path = (
                        self._extract_data_with_path_tracking(
                            source_results, source_handle_id, source_transition_id
                        )
                    )

                    if extracted_value is not None:
                        validation_detail.update(
                            {
                                "data_available": True,
                                "compatibility": "compatible",
                                "extraction_path": extraction_path,
                                "data_type": type(extracted_value).__name__,
                            }
                        )
                        validation_report["compatible_mappings"] += 1
                    else:
                        validation_detail["issues"].append(
                            f"No data available for handle {source_handle_id}"
                        )
                        validation_report["incompatible_mappings"] += 1

                validation_report["validation_details"].append(validation_detail)

        # Determine overall compatibility
        if validation_report["total_mappings"] == 0:
            validation_report["overall_compatibility"] = "no_mappings"
        elif (
            validation_report["compatible_mappings"]
            == validation_report["total_mappings"]
        ):
            validation_report["overall_compatibility"] = "fully_compatible"
        elif validation_report["compatible_mappings"] > 0:
            validation_report["overall_compatibility"] = "partially_compatible"
        else:
            validation_report["overall_compatibility"] = "incompatible"

        self.logger.info(
            f"🔍 Handle mapping validation: {validation_report['overall_compatibility']} "
            f"({validation_report['compatible_mappings']}/{validation_report['total_mappings']} compatible)"
        )

        return validation_report

    def create_comprehensive_handle_validator(self, workflow_data, transitions_data):
        """
        Create comprehensive handle validation system for any workflow.

        This method provides complete validation of handle connections across
        the entire workflow, supporting any node types and workflow complexity.

        Args:
            workflow_data: Complete workflow data with nodes and edges
            transitions_data: Transition data with handle mappings

        Returns:
            dict: Comprehensive validation report
        """
        validation_report = {
            "workflow_validation": {
                "overall_status": "unknown",
                "total_handle_connections": 0,
                "valid_connections": 0,
                "invalid_connections": 0,
                "warning_connections": 0,
                "one_to_one_constraint_status": "unknown",
                "one_to_one_violations": 0,
            },
            "node_validation": {},
            "connection_validation": [],
            "handle_conflicts": [],
            "one_to_one_violations": [],
            "recommendations": [],
            "debugging_info": {},
        }

        self.logger.info("🔍 Starting comprehensive handle validation")

        # Validate individual nodes
        nodes = workflow_data.get("nodes", [])
        for node in nodes:
            node_id = node.get("id", "unknown")
            node_validation = self._validate_node_handles(node)
            validation_report["node_validation"][node_id] = node_validation

            if node_validation["status"] == "invalid":
                validation_report["workflow_validation"]["invalid_connections"] += 1
            elif node_validation["status"] == "warning":
                validation_report["workflow_validation"]["warning_connections"] += 1
            else:
                validation_report["workflow_validation"]["valid_connections"] += 1

        # Validate handle connections across transitions
        for transition in transitions_data:
            transition_id = transition.get("id", "unknown")
            input_data_configs = transition.get("node_info", {}).get("input_data", [])

            for input_config in input_data_configs:
                handle_mappings = input_config.get("handle_mappings", [])
                for mapping in handle_mappings:
                    connection_validation = self._validate_handle_connection(
                        mapping, workflow_data
                    )
                    connection_validation["transition_id"] = transition_id
                    validation_report["connection_validation"].append(
                        connection_validation
                    )
                    validation_report["workflow_validation"][
                        "total_handle_connections"
                    ] += 1

        # Detect handle conflicts
        handle_conflicts = self._detect_handle_conflicts(
            workflow_data, transitions_data
        )
        validation_report["handle_conflicts"] = handle_conflicts

        # Validate one-to-one constraint
        one_to_one_violations = self._validate_one_to_one_constraint(workflow_data)
        validation_report["one_to_one_violations"] = one_to_one_violations
        validation_report["workflow_validation"]["one_to_one_violations"] = len(
            one_to_one_violations
        )

        if len(one_to_one_violations) == 0:
            validation_report["workflow_validation"][
                "one_to_one_constraint_status"
            ] = "valid"
        else:
            validation_report["workflow_validation"][
                "one_to_one_constraint_status"
            ] = "violated"

        # Generate comprehensive recommendations
        self._generate_workflow_recommendations(validation_report)

        # Determine overall status
        total_connections = validation_report["workflow_validation"][
            "total_handle_connections"
        ]
        valid_connections = validation_report["workflow_validation"][
            "valid_connections"
        ]
        invalid_connections = validation_report["workflow_validation"][
            "invalid_connections"
        ]

        if total_connections == 0:
            validation_report["workflow_validation"][
                "overall_status"
            ] = "no_connections"
        elif invalid_connections == 0:
            validation_report["workflow_validation"]["overall_status"] = "valid"
        elif valid_connections > invalid_connections:
            validation_report["workflow_validation"]["overall_status"] = "mostly_valid"
        else:
            validation_report["workflow_validation"]["overall_status"] = "invalid"

        self.logger.info(
            f"🎯 Workflow validation complete: {validation_report['workflow_validation']['overall_status']} "
            f"({valid_connections}/{total_connections} valid connections)"
        )

        return validation_report

    def _validate_node_handles(self, node):
        """
        Validate handles for a single node.

        Args:
            node: Node data to validate

        Returns:
            dict: Node validation results
        """
        node_validation = {
            "node_id": node.get("id", "unknown"),
            "node_type": node.get("data", {}).get("type", "unknown"),
            "status": "valid",
            "issues": [],
            "handle_info": {
                "input_handles": [],
                "output_handles": [],
                "total_handles": 0,
            },
        }

        node_data = node.get("data", {})
        node_definition = node_data.get("definition", {})

        # Extract input handles
        inputs = node_definition.get("inputs", [])
        for input_def in inputs:
            handle_info = {
                "handle_id": input_def.get("name", ""),
                "data_type": input_def.get("data_type", "unknown"),
                "required": input_def.get("required", False),
            }
            node_validation["handle_info"]["input_handles"].append(handle_info)

        # Extract output handles
        outputs = node_definition.get("outputs", [])
        for output_def in outputs:
            handle_info = {
                "handle_id": output_def.get("name", ""),
                "data_type": output_def.get("data_type", "unknown"),
            }
            node_validation["handle_info"]["output_handles"].append(handle_info)

        node_validation["handle_info"]["total_handles"] = len(inputs) + len(outputs)

        # Validate handle definitions
        if not inputs and not outputs:
            node_validation["status"] = "warning"
            node_validation["issues"].append(
                "Node has no defined input or output handles"
            )

        # Check for duplicate handle names
        input_handle_names = [input_def.get("name", "") for input_def in inputs]
        output_handle_names = [output_def.get("name", "") for output_def in outputs]
        all_handle_names = input_handle_names + output_handle_names
        duplicates = [
            name for name in set(all_handle_names) if all_handle_names.count(name) > 1
        ]
        if duplicates:
            node_validation["status"] = "invalid"
            node_validation["issues"].append(f"Duplicate handle names: {duplicates}")

        return node_validation

    def _validate_handle_connection(self, mapping, workflow_data):
        """
        Validate a single handle connection.

        Args:
            mapping: Handle mapping to validate
            workflow_data: Complete workflow data for context

        Returns:
            dict: Connection validation results
        """
        connection_validation = {
            "source_handle_id": mapping.get("source_handle_id", ""),
            "target_handle_id": mapping.get("target_handle_id", ""),
            "edge_id": mapping.get("edge_id", ""),
            "status": "valid",
            "issues": [],
            "compatibility": "unknown",
        }

        source_handle_id = mapping.get("source_handle_id")
        target_handle_id = mapping.get("target_handle_id")

        if not source_handle_id or not target_handle_id:
            connection_validation["status"] = "invalid"
            connection_validation["issues"].append("Missing source or target handle ID")
            return connection_validation

        # Find source and target nodes from workflow data
        # This would require additional logic to map handles to nodes
        # For now, we'll do basic validation

        # Check for self-connections (handle connecting to itself)
        if source_handle_id == target_handle_id:
            connection_validation["status"] = "warning"
            connection_validation["issues"].append("Handle connects to itself")

        # Additional validation logic would go here
        # (type compatibility, node existence, etc.)

        return connection_validation

    def _detect_handle_conflicts(self, workflow_data, transitions_data):
        """
        Detect handle conflicts in the workflow.

        Args:
            workflow_data: Complete workflow data
            transitions_data: Transition data with handle mappings

        Returns:
            list: List of detected conflicts
        """
        conflicts = []
        handle_usage = {}  # Track how handles are used

        # Analyze handle usage patterns
        for transition in transitions_data:
            input_data_configs = transition.get("node_info", {}).get("input_data", [])

            for input_config in input_data_configs:
                handle_mappings = input_config.get("handle_mappings", [])
                for mapping in handle_mappings:
                    source_handle = mapping.get("source_handle_id")
                    target_handle = mapping.get("target_handle_id")

                    # Track target handle usage
                    if target_handle not in handle_usage:
                        handle_usage[target_handle] = []
                    handle_usage[target_handle].append(
                        {
                            "source": source_handle,
                            "transition": transition.get("id"),
                            "mapping": mapping,
                        }
                    )

        # Detect conflicts (multiple sources to same target)
        for target_handle, usages in handle_usage.items():
            if len(usages) > 1:
                conflict = {
                    "type": "multiple_sources",
                    "target_handle": target_handle,
                    "sources": [usage["source"] for usage in usages],
                    "affected_transitions": [usage["transition"] for usage in usages],
                    "severity": "high" if len(usages) > 2 else "medium",
                }
                conflicts.append(conflict)

        return conflicts

    def _validate_one_to_one_constraint(self, workflow_data):
        """
        Validate one-to-one handle mapping constraint.

        This method checks that each target handle receives data from exactly
        one source handle, enforcing the one-to-one constraint.

        Args:
            workflow_data: Complete workflow data with edges

        Returns:
            list: List of one-to-one constraint violations
        """
        violations = []
        edges = workflow_data.get("edges", [])

        # Track target handles and their sources
        target_handle_sources = (
            {}
        )  # {(target_node, target_handle): [(source_node, source_handle, edge_id), ...]}

        for edge in edges:
            source_node = edge.get("source")
            target_node = edge.get("target")
            source_handle = edge.get("sourceHandle")
            target_handle = edge.get("targetHandle")
            edge_id = edge.get("id", "unknown")

            # Skip edges without handle information (e.g., flow connections)
            if not source_handle or not target_handle:
                continue

            # Create unique target handle identifier
            target_key = (target_node, target_handle)

            # Track this source for the target handle
            if target_key not in target_handle_sources:
                target_handle_sources[target_key] = []

            target_handle_sources[target_key].append(
                (source_node, source_handle, edge_id)
            )

        # Check for violations of one-to-one constraint
        for target_key, sources in target_handle_sources.items():
            if len(sources) > 1:
                target_node, target_handle = target_key
                violation = {
                    "type": "one_to_one_constraint_violation",
                    "target_node": target_node,
                    "target_handle": target_handle,
                    "target_identifier": f"{target_node}.{target_handle}",
                    "sources": [
                        {
                            "source_node": source_node,
                            "source_handle": source_handle,
                            "source_identifier": f"{source_node}.{source_handle}",
                            "edge_id": edge_id,
                        }
                        for source_node, source_handle, edge_id in sources
                    ],
                    "violation_count": len(sources),
                    "severity": "critical",
                    "message": f"Target handle '{target_node}.{target_handle}' receives data from {len(sources)} sources, violating one-to-one constraint",
                }
                violations.append(violation)

        if violations:
            self.logger.warning(
                f"⚠️ One-to-one constraint violations detected: {len(violations)} violations found"
            )
        else:
            self.logger.info("✅ One-to-one constraint validation passed")

        return violations

    def _generate_workflow_recommendations(self, validation_report):
        """
        Generate comprehensive recommendations for workflow optimization.

        Args:
            validation_report: Validation report to enhance with recommendations
        """
        recommendations = []

        # Recommendations based on one-to-one constraint violations
        one_to_one_violations = validation_report.get("one_to_one_violations", [])
        if one_to_one_violations:
            recommendations.append(
                f"🚨 CRITICAL: Fix {len(one_to_one_violations)} one-to-one constraint violations before execution"
            )
            for violation in one_to_one_violations[:3]:  # Show first 3 violations
                target_id = violation["target_identifier"]
                source_count = violation["violation_count"]
                recommendations.append(
                    f"   - {target_id} has {source_count} sources (must have exactly 1)"
                )
            if len(one_to_one_violations) > 3:
                recommendations.append(
                    f"   - ... and {len(one_to_one_violations) - 3} more violations"
                )

        # Recommendations based on overall status
        overall_status = validation_report["workflow_validation"]["overall_status"]
        if overall_status == "invalid":
            recommendations.append(
                "🚨 Critical: Fix invalid handle connections before execution"
            )
        elif overall_status == "mostly_valid":
            recommendations.append("⚠️ Warning: Some handle connections need attention")
        elif overall_status == "valid":
            recommendations.append("✅ All handle connections are valid")

        # Recommendations based on conflicts
        conflicts = validation_report["handle_conflicts"]
        if conflicts:
            high_severity_conflicts = [
                c for c in conflicts if c.get("severity") == "high"
            ]
            if high_severity_conflicts:
                recommendations.append(
                    f"🔥 High priority: Resolve {len(high_severity_conflicts)} high-severity handle conflicts"
                )
            else:
                recommendations.append(
                    f"⚠️ Resolve {len(conflicts)} handle conflicts for optimal performance"
                )

        # Recommendations based on node validation
        invalid_nodes = [
            node_id
            for node_id, validation in validation_report["node_validation"].items()
            if validation["status"] == "invalid"
        ]
        if invalid_nodes:
            recommendations.append(
                f"🔧 Fix handle definitions in {len(invalid_nodes)} nodes: {invalid_nodes}"
            )

        validation_report["recommendations"] = recommendations

    def create_handle_debugging_tools(
        self, workflow_data, transitions_data, execution_results=None
    ):
        """
        Create comprehensive debugging tools for handle mapping issues.

        Args:
            workflow_data: Complete workflow data
            transitions_data: Transition data with handle mappings
            execution_results: Optional execution results for runtime debugging

        Returns:
            dict: Debugging tools and analysis
        """
        debugging_tools = {
            "handle_map": {},
            "connection_graph": {},
            "execution_analysis": {},
            "troubleshooting_guide": [],
        }

        # Create handle map
        for transition in transitions_data:
            transition_id = transition.get("id")
            input_data_configs = transition.get("node_info", {}).get("input_data", [])

            for input_config in input_data_configs:
                handle_mappings = input_config.get("handle_mappings", [])
                for mapping in handle_mappings:
                    source_handle = mapping.get("source_handle_id")
                    target_handle = mapping.get("target_handle_id")

                    if source_handle not in debugging_tools["handle_map"]:
                        debugging_tools["handle_map"][source_handle] = []

                    debugging_tools["handle_map"][source_handle].append(
                        {
                            "target": target_handle,
                            "transition": transition_id,
                            "edge_id": mapping.get("edge_id"),
                        }
                    )

        # Analyze execution results if provided
        if execution_results:
            debugging_tools["execution_analysis"] = (
                self._analyze_execution_for_debugging(
                    execution_results, debugging_tools["handle_map"]
                )
            )

        # Generate troubleshooting guide
        debugging_tools["troubleshooting_guide"] = self._generate_troubleshooting_guide(
            workflow_data, transitions_data
        )

        return debugging_tools

    def _analyze_execution_for_debugging(self, execution_results, handle_map):
        """
        Analyze execution results for debugging handle issues.

        Args:
            execution_results: Results from workflow execution
            handle_map: Handle mapping information

        Returns:
            dict: Execution analysis for debugging
        """
        analysis = {
            "successful_mappings": [],
            "failed_mappings": [],
            "data_availability": {},
            "recommendations": [],
        }

        # This would analyze actual execution results
        # Implementation depends on the structure of execution_results

        return analysis

    def _generate_troubleshooting_guide(self, workflow_data, transitions_data):
        """
        Generate troubleshooting guide for common handle mapping issues.

        Args:
            workflow_data: Complete workflow data
            transitions_data: Transition data

        Returns:
            list: Troubleshooting steps and solutions
        """
        guide = [
            {
                "issue": "Handle mapping not working",
                "solutions": [
                    "Check if source handle exists in node output definition",
                    "Verify target handle exists in node input definition",
                    "Ensure handle data types are compatible",
                    "Check if source transition completed successfully",
                ],
            },
            {
                "issue": "Data not found for handle",
                "solutions": [
                    "Use result structure analyzer to understand data format",
                    "Check if result is nested under 'result' or 'data' fields",
                    "Verify the source node actually produces the expected output",
                    "Check execution logs for source transition errors",
                ],
            },
            {
                "issue": "Multiple sources conflict",
                "solutions": [
                    "Use different target handle names to avoid conflicts",
                    "Implement data merging logic if multiple sources are intentional",
                    "Review workflow design to eliminate unnecessary connections",
                    "Use conditional routing to select appropriate source",
                ],
            },
        ]

        return guide

    def create_performance_optimized_handle_resolver(
        self, workflow_data, transitions_data
    ):
        """
        Create performance-optimized handle resolver for large workflows.

        This method creates optimized data structures and caching mechanisms
        to ensure efficient handle resolution even in complex workflows.

        Args:
            workflow_data: Complete workflow data
            transitions_data: Transition data with handle mappings

        Returns:
            dict: Optimized resolver with caching and performance metrics
        """
        import time
        from collections import defaultdict

        start_time = time.time()

        resolver = {
            "handle_lookup_cache": {},
            "connection_graph": defaultdict(list),
            "performance_metrics": {
                "initialization_time": 0,
                "cache_hit_rate": 0,
                "total_lookups": 0,
                "cache_hits": 0,
            },
            "optimization_strategies": [],
        }

        # Build optimized handle lookup cache
        for transition in transitions_data:
            transition_id = transition.get("id")
            input_data_configs = transition.get("node_info", {}).get("input_data", [])

            for input_config in input_data_configs:
                handle_mappings = input_config.get("handle_mappings", [])
                for mapping in handle_mappings:
                    source_handle = mapping.get("source_handle_id")
                    target_handle = mapping.get("target_handle_id")
                    source_transition = mapping.get("source_transition_id")

                    # Create fast lookup key
                    lookup_key = f"{source_transition}:{source_handle}"
                    if lookup_key not in resolver["handle_lookup_cache"]:
                        resolver["handle_lookup_cache"][lookup_key] = []

                    resolver["handle_lookup_cache"][lookup_key].append(
                        {
                            "target_transition": transition_id,
                            "target_handle": target_handle,
                            "mapping": mapping,
                        }
                    )

                    # Build connection graph for optimization analysis
                    resolver["connection_graph"][source_transition].append(
                        transition_id
                    )

        # Analyze optimization opportunities
        resolver["optimization_strategies"] = self._analyze_optimization_opportunities(
            resolver["connection_graph"], len(workflow_data.get("nodes", []))
        )

        resolver["performance_metrics"]["initialization_time"] = (
            time.time() - start_time
        )

        self.logger.info(
            f"🚀 Performance-optimized resolver created: "
            f"{len(resolver['handle_lookup_cache'])} cached lookups, "
            f"init time: {resolver['performance_metrics']['initialization_time']:.3f}s"
        )

        return resolver

    def _analyze_optimization_opportunities(self, connection_graph, node_count):
        """
        Analyze workflow for optimization opportunities.

        Args:
            connection_graph: Graph of node connections
            node_count: Total number of nodes

        Returns:
            list: Optimization strategies
        """
        strategies = []

        # Analyze workflow complexity
        if node_count > 20:
            strategies.append(
                {
                    "strategy": "batch_processing",
                    "description": "Use batch processing for large workflows",
                    "estimated_improvement": "30-50% faster execution",
                }
            )

        # Analyze connection patterns
        max_connections = (
            max(len(connections) for connections in connection_graph.values())
            if connection_graph
            else 0
        )
        if max_connections > 5:
            strategies.append(
                {
                    "strategy": "parallel_execution",
                    "description": "Enable parallel execution for independent branches",
                    "estimated_improvement": "20-40% faster execution",
                }
            )

        # Analyze for potential caching opportunities
        total_connections = sum(
            len(connections) for connections in connection_graph.values()
        )
        if total_connections > node_count * 1.5:
            strategies.append(
                {
                    "strategy": "aggressive_caching",
                    "description": "Use aggressive caching for frequently accessed handles",
                    "estimated_improvement": "15-25% faster resolution",
                }
            )

        return strategies

    def create_comprehensive_error_handler(self):
        """
        Create comprehensive error handling system for handle resolution.

        Returns:
            dict: Error handling configuration and utilities
        """
        error_handler = {
            "error_categories": {
                "MALFORMED_MAPPING": {
                    "severity": "high",
                    "recovery_strategy": "skip_mapping",
                    "user_message": "Handle mapping is malformed and will be skipped",
                },
                "MISSING_SOURCE_DATA": {
                    "severity": "medium",
                    "recovery_strategy": "use_default",
                    "user_message": "Source data not found, using default value",
                },
                "TYPE_MISMATCH": {
                    "severity": "low",
                    "recovery_strategy": "attempt_conversion",
                    "user_message": "Data type mismatch detected, attempting conversion",
                },
                "CIRCULAR_DEPENDENCY": {
                    "severity": "critical",
                    "recovery_strategy": "break_cycle",
                    "user_message": "Circular dependency detected in handle mappings",
                },
                "TIMEOUT": {
                    "severity": "high",
                    "recovery_strategy": "abort_resolution",
                    "user_message": "Handle resolution timeout exceeded",
                },
            },
            "recovery_strategies": {
                "skip_mapping": self._skip_malformed_mapping,
                "use_default": self._use_default_value,
                "attempt_conversion": self._attempt_type_conversion,
                "break_cycle": self._break_circular_dependency,
                "abort_resolution": self._abort_resolution,
            },
            "error_metrics": {
                "total_errors": 0,
                "errors_by_category": defaultdict(int),
                "recovery_success_rate": 0,
                "critical_errors": 0,
            },
        }

        return error_handler

    def _skip_malformed_mapping(self, mapping, error_context):
        """Skip malformed mapping and log warning."""
        self.logger.warning(
            f"⚠️ Skipping malformed mapping: {mapping} - {error_context.get('error', 'Unknown error')}"
        )
        return None

    def _use_default_value(self, mapping, error_context):
        """Use default value when source data is missing."""
        default_value = error_context.get("default_value", "")
        self.logger.info(
            f"🔄 Using default value '{default_value}' for missing source: {mapping.get('source_handle_id')}"
        )
        return default_value

    def _attempt_type_conversion(self, value, target_type, error_context):
        """Attempt to convert value to target type."""
        try:
            if target_type == "string":
                return str(value)
            elif target_type == "number":
                return float(value) if "." in str(value) else int(value)
            elif target_type == "boolean":
                return bool(value)
            else:
                return value
        except Exception as e:
            self.logger.warning(f"⚠️ Type conversion failed: {str(e)}")
            return value

    def _break_circular_dependency(self, cycle_info, error_context):
        """Break circular dependency by removing weakest link."""
        self.logger.error(
            f"🔥 Breaking circular dependency: {cycle_info} - removing weakest connection"
        )
        # Implementation would analyze and break the cycle
        return {
            "cycle_broken": True,
            "removed_connection": cycle_info.get("weakest_link"),
        }

    def _abort_resolution(self, timeout_info, error_context):
        """Abort resolution due to timeout."""
        self.logger.error(
            f"⏰ Aborting handle resolution due to timeout: {timeout_info}"
        )
        raise TimeoutError(f"Handle resolution timeout exceeded: {timeout_info}")

    def create_enhanced_logging_system(self):
        """
        Create enhanced logging system for handle resolution debugging.

        Returns:
            dict: Logging configuration and utilities
        """
        import logging
        from datetime import datetime

        logging_system = {
            "loggers": {
                "handle_resolution": logging.getLogger("handle_resolution"),
                "performance": logging.getLogger("performance"),
                "validation": logging.getLogger("validation"),
                "debugging": logging.getLogger("debugging"),
            },
            "log_levels": {
                "handle_resolution": logging.INFO,
                "performance": logging.DEBUG,
                "validation": logging.WARNING,
                "debugging": logging.DEBUG,
            },
            "formatters": {
                "detailed": logging.Formatter(
                    "%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s"
                ),
                "performance": logging.Formatter("%(asctime)s - PERF - %(message)s"),
                "simple": logging.Formatter("%(levelname)s - %(message)s"),
            },
            "metrics": {
                "log_counts": defaultdict(int),
                "performance_logs": [],
                "error_logs": [],
                "debug_sessions": [],
            },
        }

        # Configure loggers
        for logger_name, logger in logging_system["loggers"].items():
            logger.setLevel(logging_system["log_levels"][logger_name])

            # Add console handler with appropriate formatter
            console_handler = logging.StreamHandler()
            if logger_name == "performance":
                console_handler.setFormatter(
                    logging_system["formatters"]["performance"]
                )
            elif logger_name in ["validation", "debugging"]:
                console_handler.setFormatter(logging_system["formatters"]["detailed"])
            else:
                console_handler.setFormatter(logging_system["formatters"]["simple"])

            logger.addHandler(console_handler)

        return logging_system

    def benchmark_handle_resolution_performance(self, test_scenarios):
        """
        Benchmark handle resolution performance across different scenarios.

        Args:
            test_scenarios: List of test scenarios with varying complexity

        Returns:
            dict: Performance benchmark results
        """
        import time
        import statistics

        benchmark_results = {
            "scenarios": {},
            "summary": {
                "total_scenarios": len(test_scenarios),
                "average_resolution_time": 0,
                "fastest_scenario": None,
                "slowest_scenario": None,
                "performance_grade": "unknown",
            },
        }

        all_times = []

        for scenario in test_scenarios:
            scenario_name = scenario.get("name", "unnamed")
            handle_mappings = scenario.get("handle_mappings", {})
            previous_results = scenario.get("previous_results", {})

            # Benchmark multiple runs for accuracy
            run_times = []
            for run in range(5):  # 5 runs per scenario
                start_time = time.time()

                try:
                    # Perform handle resolution
                    parameter_mapping = self.create_universal_parameter_mapping(
                        handle_mappings, previous_results
                    )
                    resolution_time = time.time() - start_time
                    run_times.append(resolution_time)

                except Exception as e:
                    self.logger.error(
                        f"❌ Benchmark failed for scenario {scenario_name}: {str(e)}"
                    )
                    run_times.append(float("inf"))  # Mark as failed

            # Calculate scenario statistics
            valid_times = [t for t in run_times if t != float("inf")]
            if valid_times:
                scenario_stats = {
                    "average_time": statistics.mean(valid_times),
                    "min_time": min(valid_times),
                    "max_time": max(valid_times),
                    "std_dev": (
                        statistics.stdev(valid_times) if len(valid_times) > 1 else 0
                    ),
                    "success_rate": len(valid_times) / len(run_times),
                    "complexity": scenario.get("complexity", "unknown"),
                }
                all_times.extend(valid_times)
            else:
                scenario_stats = {
                    "average_time": float("inf"),
                    "success_rate": 0,
                    "error": "All runs failed",
                }

            benchmark_results["scenarios"][scenario_name] = scenario_stats

        # Calculate summary statistics
        if all_times:
            benchmark_results["summary"]["average_resolution_time"] = statistics.mean(
                all_times
            )

            # Find fastest and slowest scenarios
            scenario_averages = {
                name: stats.get("average_time", float("inf"))
                for name, stats in benchmark_results["scenarios"].items()
                if stats.get("average_time", float("inf")) != float("inf")
            }

            if scenario_averages:
                benchmark_results["summary"]["fastest_scenario"] = min(
                    scenario_averages, key=scenario_averages.get
                )
                benchmark_results["summary"]["slowest_scenario"] = max(
                    scenario_averages, key=scenario_averages.get
                )

            # Assign performance grade
            avg_time = benchmark_results["summary"]["average_resolution_time"]
            if avg_time < 0.001:  # < 1ms
                benchmark_results["summary"]["performance_grade"] = "excellent"
            elif avg_time < 0.01:  # < 10ms
                benchmark_results["summary"]["performance_grade"] = "good"
            elif avg_time < 0.1:  # < 100ms
                benchmark_results["summary"]["performance_grade"] = "acceptable"
            else:
                benchmark_results["summary"]["performance_grade"] = "needs_optimization"

        self.logger.info(
            f"📊 Performance benchmark complete: "
            f"avg {benchmark_results['summary']['average_resolution_time']:.4f}s, "
            f"grade: {benchmark_results['summary']['performance_grade']}"
        )

        return benchmark_results

    def _format_params_according_to_schema(self, node_tool_info, params):
        """
        Format parameters according to the input schema structure.

        This method now includes automatic null/empty value filtering to ensure
        only meaningful parameters are included in the final formatted parameters.

        Args:
            node_tool_info: Dictionary containing the input schema
            params: Parameters to format (can be a list or dict)

        Returns:
            dict: Parameters formatted according to the input schema with null/empty values filtered out
        """
        input_schema = node_tool_info.get("input_schema", {})

        # If there's no input schema, return the params as is
        if not input_schema:
            return params

        # Extract the predefined fields from the input schema
        predefined_fields = input_schema.get("predefined_fields", [])
        if not predefined_fields:
            return params

        # Create a dictionary to store the formatted parameters
        formatted_params = {}

        # Convert params to a dictionary if it's a list
        param_dict = {}
        if isinstance(params, list):
            for param_item in params:
                if isinstance(param_item, dict):
                    field_name = param_item.get("field_name")
                    field_value = param_item.get("field_value")
                    if field_name:
                        param_dict[field_name] = field_value
        else:
            param_dict = params if isinstance(params, dict) else {}

        # Fill in the values for each field in the input schema
        total_schema_fields = len(predefined_fields)
        processed_fields = 0
        skipped_null_fields = 0

        for field in predefined_fields:
            field_name = field.get("field_name")
            if not field_name:
                self.logger.debug(f"Skipping field with no field_name: {field}")
                continue

            # Get the data type for this field
            data_type = field.get("data_type", {})
            field_type = data_type.get("type", "string")
            is_required = field.get("required", False)

            # Get the value for this field from the params
            field_value = param_dict.get(field_name)

            # Pre-filter null/empty values before adding to formatted_params
            if field_value is None or field_value == "" or field_value == "null":
                if is_required:
                    self.logger.warning(
                        f"Required field '{field_name}' has null/empty value: {field_value}"
                    )
                    # Include required fields even if null (let the tool handle the error)
                    formatted_params[field_name] = field_value
                    processed_fields += 1
                else:
                    self.logger.debug(
                        f"Skipping optional field '{field_name}' with null/empty value: {field_value}"
                    )
                    skipped_null_fields += 1
                continue

            # Skip empty collections for optional fields
            if isinstance(field_value, (dict, list)) and len(field_value) == 0:
                if is_required:
                    self.logger.warning(
                        f"Required field '{field_name}' has empty collection: {field_value}"
                    )
                    formatted_params[field_name] = field_value
                    processed_fields += 1
                else:
                    self.logger.debug(
                        f"Skipping optional field '{field_name}' with empty collection: {field_value}"
                    )
                    skipped_null_fields += 1
                continue

            # Handle nested objects
            if field_type == "object" and isinstance(field_value, dict):
                # For object types, recursively filter nested properties
                filtered_nested = self._filter_null_empty_values(field_value)
                if filtered_nested or is_required:
                    formatted_params[field_name] = filtered_nested
                    processed_fields += 1
                else:
                    self.logger.debug(
                        f"Skipping optional field '{field_name}' with empty nested object after filtering"
                    )
                    skipped_null_fields += 1
            else:
                # For other types, include the value
                formatted_params[field_name] = field_value
                processed_fields += 1

        # Log schema processing summary
        if skipped_null_fields > 0:
            self.logger.info(
                f"🧹 Schema formatting: {processed_fields}/{total_schema_fields} fields processed "
                f"({skipped_null_fields} null/empty fields skipped)"
            )
        else:
            self.logger.debug(
                f"Schema formatting: {processed_fields}/{total_schema_fields} fields processed"
            )

        self.logger.debug(
            f"Formatted parameters according to schema: {formatted_params}"
        )

        # Apply final null/empty value filtering to formatted parameters
        # (This is a safety net in case any null values slipped through)
        original_formatted_count = len(formatted_params)
        filtered_params = self._filter_null_empty_values(formatted_params)
        final_filtered_count = len(filtered_params)

        if original_formatted_count != final_filtered_count:
            self.logger.info(
                f"🧹 Final schema filtering: {original_formatted_count} → {final_filtered_count} parameters "
                f"({original_formatted_count - final_filtered_count} additional null/empty parameters removed)"
            )
        else:
            self.logger.debug(
                "No additional null/empty parameters found in final schema filtering"
            )

        self.logger.debug(f"Final filtered schema parameters: {filtered_params}")
        return filtered_params

    def _process_params_for_placeholders(self, params, flattened_results):
        """
        Process parameters to replace placeholders with values from flattened_results.
        Always returns a dictionary with direct key-value pairs.

        Args:
            params: The parameters to process (can be a list or dict)
            flattened_results: Dictionary of field values from previous results

        Returns:
            tuple: (processed_params, has_unresolved_placeholders)
        """
        has_unresolved_placeholders = False
        processed_params = {}

        if isinstance(params, list):
            # Convert list format to dictionary format
            for param_item in params:
                if isinstance(param_item, dict):
                    field_name = param_item.get("field_name", "")
                    field_value = param_item.get("field_value", "")

                    if not field_name:
                        continue

                    # Check if field_value contains a placeholder - only process ${var} format, preserve ${{var}} format
                    if (
                        isinstance(field_value, str)
                        and ("${" in field_value)
                        and ("}" in field_value)
                    ):
                        # Only try to resolve single brace ${var} format - preserve double brace ${{var}} for agent-platform
                        placeholder_match = re.search(r"\$\{([^{].*?)\}", field_value)
                        if placeholder_match:
                            placeholder = placeholder_match.group(1)
                            self.logger.debug(
                                f"Found orchestration placeholder: {placeholder} in field: {field_name}"
                            )

                            # Look for the placeholder in flattened results
                            if placeholder in flattened_results:
                                field_value = flattened_results[placeholder]
                                self.logger.debug(
                                    f"Replaced orchestration placeholder '{placeholder}' with value: {field_value}"
                                )
                            else:
                                # Mark that we have unresolved placeholders
                                has_unresolved_placeholders = True
                                self.logger.debug(
                                    f"Could not resolve orchestration placeholder '{placeholder}' in field: {field_name}"
                                )
                        else:
                            # Check if it contains ${{var}} format - these should be preserved for agent-platform
                            if "${{" in field_value and "}}" in field_value:
                                self.logger.debug(
                                    f"Found agent-platform template variables in field: {field_name} - preserving for agent-platform"
                                )

                    # Handle nested objects (like keywords)
                    if isinstance(field_value, dict):
                        # Process nested dictionary for placeholders
                        nested_dict, nested_unresolved = self._process_nested_dict(
                            field_value, flattened_results
                        )
                        if nested_unresolved:
                            has_unresolved_placeholders = True
                        processed_params[field_name] = nested_dict
                    else:
                        processed_params[field_name] = field_value

            # Apply null/empty value filtering to processed parameters
            filtered_params = self._filter_null_empty_values(processed_params)
            return filtered_params, has_unresolved_placeholders

        elif isinstance(params, dict):
            # Handle case where params is already a dictionary
            for key, value in params.items():
                if isinstance(value, str) and ("${" in value) and ("}" in value):
                    # Only try to resolve single brace ${var} format - preserve double brace ${{var}} for agent-platform
                    placeholder_match = re.search(r"\$\{([^{].*?)\}", value)
                    if placeholder_match:
                        placeholder = placeholder_match.group(1)
                        self.logger.debug(
                            f"Found orchestration placeholder: {placeholder} in key: {key}"
                        )

                        # Look for the placeholder in flattened results
                        if placeholder in flattened_results:
                            value = flattened_results[placeholder]
                            self.logger.debug(
                                f"Replaced orchestration placeholder '{placeholder}' with value: {value}"
                            )
                        else:
                            # Mark that we have unresolved placeholders
                            has_unresolved_placeholders = True
                            self.logger.debug(
                                f"Could not resolve orchestration placeholder '{placeholder}' in key: {key}"
                            )
                    else:
                        # Check if it contains ${{var}} format - these should be preserved for agent-platform
                        if "${{" in value and "}}" in value:
                            self.logger.debug(
                                f"Found agent-platform template variables in key: {key} - preserving for agent-platform"
                            )
                elif isinstance(value, dict):
                    # Process nested dictionary for placeholders
                    nested_dict, nested_unresolved = self._process_nested_dict(
                        value, flattened_results
                    )
                    if nested_unresolved:
                        has_unresolved_placeholders = True
                    value = nested_dict

                processed_params[key] = value

            # Apply null/empty value filtering to processed parameters
            filtered_params = self._filter_null_empty_values(processed_params)
            return filtered_params, has_unresolved_placeholders

        # If params is neither a list nor a dict, return an empty dict
        return {}, False

    def _process_nested_dict(self, nested_dict, flattened_results):
        """
        Process a nested dictionary for placeholders.

        Args:
            nested_dict: The nested dictionary to process
            flattened_results: Dictionary of field values from previous results

        Returns:
            tuple: (processed_nested_dict, has_unresolved_placeholders)
        """
        has_unresolved_placeholders = False
        processed_nested_dict = {}

        for key, value in nested_dict.items():
            if isinstance(value, str) and ("${" in value) and ("}" in value):
                # Only try to resolve single brace ${var} format - preserve double brace ${{var}} for agent-platform
                placeholder_match = re.search(r"\$\{([^{].*?)\}", value)
                if placeholder_match:
                    placeholder = placeholder_match.group(1)
                    self.logger.debug(
                        f"Found orchestration placeholder: {placeholder} in nested key: {key}"
                    )

                    # Look for the placeholder in flattened results
                    if placeholder in flattened_results:
                        value = flattened_results[placeholder]
                        self.logger.debug(
                            f"Replaced orchestration placeholder '{placeholder}' with value: {value}"
                        )
                    else:
                        # Mark that we have unresolved placeholders
                        has_unresolved_placeholders = True
                        self.logger.debug(
                            f"Could not resolve orchestration placeholder '{placeholder}' in nested key: {key}"
                        )
                else:
                    # Check if it contains ${{var}} format - these should be preserved for agent-platform
                    if "${{" in value and "}}" in value:
                        self.logger.debug(
                            f"Found agent-platform template variables in nested key: {key} - preserving for agent-platform"
                        )
            elif isinstance(value, dict):
                # Recursively process nested dictionaries
                value, nested_unresolved = self._process_nested_dict(
                    value, flattened_results
                )
                if nested_unresolved:
                    has_unresolved_placeholders = True

            processed_nested_dict[key] = value

        return processed_nested_dict, has_unresolved_placeholders

    async def _fallback_to_format_schema(
        self,
        node_tool_info: dict,
        current_previous_results: dict,
        current_tool_params: dict,
    ) -> dict:
        """
        Fallback method that uses format_schema (AI-based) to format tool parameters.
        Used when direct placeholder replacement fails or doesn't fully resolve all placeholders.
        """
        input_schema = node_tool_info.get("input_schema", {})
        output_schema = node_tool_info.get("output_schema", {})

        self.logger.info(
            "Using AI-based format_schema as fallback for parameter formatting"
        )

        try:
            formatted_params = await format_schema(
                input_schema,
                output_schema,
                current_previous_results,
                current_tool_params,
            )
            return formatted_params
        except Exception as e:
            self.logger.error(f"AI-based formatting also failed: {str(e)}")
            # If AI-based formatting also fails, return the original params
            return current_tool_params

    async def _evaluate_switch_case(
        self,
        transition_routing: Dict[str, Any],
        node_result: Any,
    ) -> List[str]:
        """
        DEPRECATED: Switch case evaluation now handled by conditional component.

        This method is deprecated and should not be used. All conditional routing
        is now handled by the conditional component architecture.
        """
        self.logger.warning(
            "DEPRECATED: _evaluate_switch_case called. Conditional routing should use "
            "conditional components instead of embedded routing."
        )
        return []

    # Loop Node Utilities for New Architecture

    def extract_loop_output_data(self, loop_result: dict, output_type: str) -> dict:
        """
        Extract output data from loop execution result based on output type.

        Args:
            loop_result: Result from loop execution
            output_type: Type of output to extract (iteration_output, exit_output)

        Returns:
            Extracted output data
        """
        self.logger.debug(f"🔄 Extracting {output_type} data from loop result")

        if not isinstance(loop_result, dict):
            self.logger.warning(f"Loop result is not a dictionary: {type(loop_result)}")
            return {}

        # Check if this is the new dual output format
        if "output_type" in loop_result:
            result_output_type = loop_result.get("output_type")
            if result_output_type == output_type:
                # Extract the appropriate data
                if output_type == "iteration_output":
                    return loop_result.get("current_iteration_item", {})
                elif output_type == "exit_output":
                    return loop_result.get("aggregated_results", [])
            else:
                self.logger.warning(
                    f"Requested {output_type} but got {result_output_type}"
                )
                return {}

        # Legacy format handling
        if output_type == "exit_output":
            # Look for aggregated results in various formats
            if "aggregated_results" in loop_result:
                return loop_result["aggregated_results"]
            elif "results" in loop_result:
                return loop_result["results"]
            elif isinstance(loop_result, list):
                return loop_result

        return loop_result

    def get_loop_state_utilities(self, loop_id: str) -> dict:
        """
        Get utilities for managing loop state.

        Args:
            loop_id: Identifier for the loop

        Returns:
            Dictionary with loop state utility functions
        """
        return {
            "loop_id": loop_id,
            "get_iteration_count": lambda: self._get_loop_iteration_count(loop_id),
            "get_loop_status": lambda: self._get_loop_status(loop_id),
            "is_loop_completed": lambda: self._is_loop_completed(loop_id),
        }

    def _get_loop_iteration_count(self, loop_id: str) -> int:
        """
        Get the current iteration count for a loop.

        Args:
            loop_id: Identifier for the loop

        Returns:
            Current iteration count
        """
        try:
            # This would typically query the state manager for loop state
            # For now, return a default value
            return 0
        except Exception as e:
            self.logger.error(
                f"Failed to get iteration count for loop {loop_id}: {str(e)}"
            )
            return 0

    def _get_loop_status(self, loop_id: str) -> str:
        """
        Get the current status of a loop.

        Args:
            loop_id: Identifier for the loop

        Returns:
            Loop status string
        """
        try:
            # This would typically query the state manager for loop state
            # For now, return a default value
            return "unknown"
        except Exception as e:
            self.logger.error(f"Failed to get status for loop {loop_id}: {str(e)}")
            return "error"

    def _is_loop_completed(self, loop_id: str) -> bool:
        """
        Check if a loop has completed execution.

        Args:
            loop_id: Identifier for the loop

        Returns:
            True if loop is completed
        """
        try:
            status = self._get_loop_status(loop_id)
            return status in ["completed", "terminated", "failed"]
        except Exception as e:
            self.logger.error(
                f"Failed to check completion for loop {loop_id}: {str(e)}"
            )
            return False

    def validate_loop_output_routing(self, transition: dict) -> dict:
        """
        Validate loop output routing configuration.

        Args:
            transition: Loop transition configuration

        Returns:
            Validation report
        """
        self.logger.debug("🔍 Validating loop output routing configuration")

        validation_report = {
            "valid": True,
            "issues": [],
            "warnings": [],
            "output_configs": [],
        }

        try:
            node_info = transition.get("node_info", {})
            output_data_configs = node_info.get("output_data", [])

            if not output_data_configs:
                validation_report["issues"].append(
                    "No output_data configurations found"
                )
                validation_report["valid"] = False
                return validation_report

            # Check for dual output configuration
            iteration_outputs = []
            exit_outputs = []

            for config in output_data_configs:
                output_handle_registry = config.get("output_handle_registry", {})
                handle_mappings = output_handle_registry.get("handle_mappings", [])

                for mapping in handle_mappings:
                    result_path = mapping.get("result_path", "")
                    if "current_iteration" in result_path or "iteration" in result_path:
                        iteration_outputs.append(config)
                    elif (
                        "aggregated" in result_path
                        or "exit" in result_path
                        or "final" in result_path
                    ):
                        exit_outputs.append(config)

            validation_report["output_configs"] = {
                "iteration_outputs": len(iteration_outputs),
                "exit_outputs": len(exit_outputs),
                "total_outputs": len(output_data_configs),
            }

            # Validate dual output architecture
            if len(iteration_outputs) == 0:
                validation_report["warnings"].append(
                    "No iteration output configurations found"
                )

            if len(exit_outputs) == 0:
                validation_report["warnings"].append(
                    "No exit output configurations found"
                )

            if len(iteration_outputs) > 1:
                validation_report["warnings"].append(
                    "Multiple iteration output configurations found"
                )

            if len(exit_outputs) > 1:
                validation_report["warnings"].append(
                    "Multiple exit output configurations found"
                )

            self.logger.debug(
                f"✅ Loop output routing validation complete: {validation_report}"
            )

        except Exception as e:
            validation_report["valid"] = False
            validation_report["issues"].append(f"Validation error: {str(e)}")
            self.logger.error(f"Failed to validate loop output routing: {str(e)}")

        return validation_report
